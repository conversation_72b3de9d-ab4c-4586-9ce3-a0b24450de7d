import React, { useEffect, useState } from 'react';
import { useKPIDrawer } from '@/hooks/useKPIDrawer';
import { KPIDrawerContent } from './KPIDrawerContent';
import { motion, AnimatePresence } from 'framer-motion';
import KpiBentoCard from '@/components/dashboard/KpiBentoCard';
import { useKpis } from '@/hooks/useKpis';
import { useDashboardFilters } from '@/hooks/useDashboardFilters';

export const KPIDrawer: React.FC = () => {
  const { isOpen, currentKPI, originalElement, closeDrawer } = useKPIDrawer();
  const [selectedCardData, setSelectedCardData] = useState<any>(null);

  // Get filters and KPIs for the drawer card
  const { filters } = useDashboardFilters();
  const { kpis, togglePriority } = useKpis({ filters });

  // Find the current KPI data
  const currentKpiData = kpis.find(kpi => kpi.id === currentKPI);

  // Controlar scroll do body quando drawer está aberto
  useEffect(() => {
    if (isOpen) {
      // Desabilitar scroll do body
      document.body.style.overflow = 'hidden';
      document.body.style.paddingRight = '0px'; // Evitar shift de layout
    } else {
      // Reabilitar scroll do body
      document.body.style.overflow = '';
      document.body.style.paddingRight = '';
    }

    // Cleanup ao desmontar o componente
    return () => {
      document.body.style.overflow = '';
      document.body.style.paddingRight = '';
    };
  }, [isOpen]);

  // Fechar com ESC
  useEffect(() => {
    const handleEsc = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        closeDrawer();
      }
    };
    
    window.addEventListener('keydown', handleEsc);
    return () => window.removeEventListener('keydown', handleEsc);
  }, [isOpen, closeDrawer]);

  // Capturar dados do card selecionado quando drawer abre
  useEffect(() => {
    if (isOpen && originalElement && currentKPI) {
      // Clonar o conteúdo do card original
      const cardClone = originalElement.cloneNode(true) as HTMLElement;
      
      // Esconder o card original
      originalElement.style.opacity = '0.3';
      originalElement.style.pointerEvents = 'none';
      
      // Extrair dados do card
      setSelectedCardData({
        element: cardClone,
        originalElement: originalElement
      });

      return () => {
        // Restaurar o card original quando fechar
        if (originalElement) {
          originalElement.style.opacity = '';
          originalElement.style.pointerEvents = '';
        }
        setSelectedCardData(null);
      };
    }
  }, [isOpen, originalElement, currentKPI]);

  return (
    <AnimatePresence>
      {isOpen && currentKPI && (
        <>
          {/* Background Blur Overlay */}
          <motion.div 
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="fixed inset-0 z-40 bg-black/20 backdrop-blur-sm"
            data-drawer-open="true"
            onClick={closeDrawer}
          />
          
          {/* Split Screen Layout */}
          <motion.div 
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.4 }}
            className="fixed inset-0 z-50 flex"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Left Side - Selected Card */}
            <motion.div 
              initial={{ x: -300, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              exit={{ x: -300, opacity: 0 }}
              transition={{ type: 'spring', damping: 25, stiffness: 200 }}
              className="w-[45%] flex items-center justify-center p-8"
            >
              <motion.div
                initial={{ scale: 0.8 }}
                animate={{ scale: 1 }}
                transition={{ duration: 0.3, delay: 0.1 }}
                className="w-full max-w-md"
              >
                {currentKpiData && (
                  <div className="transform transition-all duration-300 hover:scale-105">
                    <KpiBentoCard
                      kpi={currentKpiData}
                      filters={filters}
                      isLarge={false}
                    />
                  </div>
                )}
              </motion.div>
            </motion.div>
            
            {/* Right Side - Drawer Content com scroll isolado */}
            <motion.div 
              initial={{ x: '100%' }}
              animate={{ x: 0 }}
              exit={{ x: '100%' }}
              transition={{ type: 'spring', damping: 25, stiffness: 200 }}
              className="w-[55%] bg-white shadow-2xl overflow-y-auto max-h-screen"
            >
              <KPIDrawerContent kpiId={currentKPI} onClose={closeDrawer} />
            </motion.div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
};
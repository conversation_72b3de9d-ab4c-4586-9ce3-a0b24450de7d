"""
Enhanced Coordinator Agent for DataHero4 - Week 4 Integration
============================================================

Coordinator aprimorado que integra:
- Preservação de contexto conversacional
- SmartQueryRouter para arquitetura híbrida
- Profile-aware routing e validação
- HybridKpiService integration

Key Features:
- Integração com Context Preservation Engine
- SmartQueryRouter para roteamento inteligente
- Profile detection e validação obrigatória
- Hybrid KPI routing baseado em perfil
- Fail-fast validation sem fallbacks
"""

import logging
from typing import Dict, Any, Optional
from datetime import datetime

from src.graphs.state import DataHeroState
from src.services.context_preservation_engine import ContextPreservationEngine
from src.services.context_validation_system import context_validation_system
from src.services.smart_query_router import get_smart_query_router
from src.services.profile_detector import get_profile_detector
from src.services.hybrid_kpi_service import get_hybrid_kpi_service

# Classe simples para comando do coordinator
class CoordinatorCommand:
    def __init__(self, next_agent: str):
        self.next_agent = next_agent

logger = logging.getLogger(__name__)

# Instância global do Context Preservation Engine (lazy loaded)
context_engine = None

def get_context_engine():
    """Get or create context engine instance."""
    global context_engine
    if context_engine is None:
        context_engine = ContextPreservationEngine()
    return context_engine


async def enhanced_coordinator_agent(state: DataHeroState) -> Dict[str, Any]:
    """
    Enhanced Coordinator Agent - Week 4 Integration

    Integra Context Preservation Engine + SmartQueryRouter para:
    - Validar user_profile obrigatório (fail-fast)
    - Detectar perfil automaticamente se necessário
    - Preservar contexto conversacional
    - Rotear via SmartQueryRouter baseado em perfil
    - Integrar HybridKpiService para KPIs personalizados

    Args:
        state: Estado atual do LangGraph

    Returns:
        Dict com próximo agente e contexto preservado + profile routing
    """
    try:
        logger.info("🎯 Enhanced Coordinator - Week 4: Starting profile-aware coordination")

        # === WEEK 4: PROFILE VALIDATION (FAIL-FAST) ===
        user_profile = state.get("user_profile")
        user_id = state.get("user_id")

        if not user_id:
            logger.error("❌ user_id is required but not provided")
            return {
                "coordinator_command": "error",
                "error": "user_id_required",
                "error_message": "user_id is required for profile-aware routing",
                "processing_metadata": {
                    "coordinator_type": "enhanced_fail_fast",
                    "error": "missing_user_id",
                    "timestamp": datetime.now().isoformat()
                }
            }

        # Profile detection if not provided
        if not user_profile:
            logger.info(f"🔍 Detecting profile for user {user_id}")
            profile_detector = get_profile_detector()
            detection_result = profile_detector.detect_profile(user_id)

            if detection_result.get('detected_profile') and detection_result.get('confidence', 0) >= 0.30:
                user_profile = detection_result['detected_profile']
                logger.info(f"✅ Profile detected: {user_profile} (confidence: {detection_result['confidence']:.2f})")
            else:
                # Default to Operations profile if detection fails
                user_profile = "Operations"
                logger.warning(f"⚠️ Profile detection failed, defaulting to: {user_profile}")

            # Update state with detected profile
            state["user_profile"] = user_profile
            state["profile_detection_result"] = detection_result

        # === CONTEXT PRESERVATION (EXISTING LOGIC) ===
        # Obter informações básicas
        question = state.get("question", "")
        thread_id = state.get("conversation_context", {}).get("thread_id", "default")
        conversation_history = state.get("message_history", [])
        
        # Processar contexto conversacional
        context_result = get_context_engine().process_query(
            thread_id=thread_id,
            query=question,
            conversation_history=conversation_history,
            query_result=state.get("query_result")
        )
        
        # Atualizar estado com contexto preservado
        context_updates = {
            "extracted_entities": context_result.entities_extracted,
            "inherited_context": context_result.inherited_context,
            "resolved_references": context_result.resolved_references,
            "context_preservation_result": context_result.to_dict(),
            "context_confidence_score": context_result.confidence_score,
            "context_applied": context_result.context_applied
        }
        
        # Separar contexto por tipo para facilitar uso
        inherited = context_result.inherited_context
        context_updates.update({
            "temporal_context": inherited.get("temporal_filters", {}),
            "business_context": inherited.get("business_filters", {}),
            "filter_context": inherited.get("active_conditions", {})
        })

        # Validar contexto disponível
        validation_result = context_validation_system.validate_context(
            query=question,
            available_context=inherited,
            conversation_history=conversation_history
        )

        context_updates["context_validation"] = validation_result.to_dict()

        # === WEEK 4: SMART QUERY ROUTER INTEGRATION ===
        # Check if this is a KPI-related query that can be handled by hybrid architecture
        smart_router = get_smart_query_router()
        hybrid_kpi_service = get_hybrid_kpi_service()

        # Try to identify KPI requests from the question
        kpi_keywords = ['kpi', 'spread', 'income', 'margin', 'cost', 'processing', 'time', 'transaction']
        is_kpi_query = any(keyword in question.lower() for keyword in kpi_keywords)

        if is_kpi_query:
            logger.info(f"🧮 Detected KPI query, attempting hybrid routing for profile: {user_profile}")

            # Try to route through SmartQueryRouter
            try:
                # For now, we'll use a default KPI - in production this would be extracted from the question
                test_kpi_id = "spread_income_detailed"  # This would be determined by NLP analysis

                routing_result = smart_router.route_kpi_request(
                    kpi_id=test_kpi_id,
                    client_id=state.get("client_id", "L2M"),
                    user_id=user_id,
                    timeframe="week",
                    currency="all",
                    profile_type=user_profile
                )

                if not routing_result.get('error'):
                    logger.info(f"✅ KPI routed successfully via {routing_result.get('source', 'hybrid')}")
                    context_updates["hybrid_kpi_results"] = routing_result
                    context_updates["routing_metadata"] = routing_result.get('routing_metadata', {})

                    # If we got a result, we can potentially skip SQL generation
                    next_agent = "business_analyst"  # Go directly to analysis
                else:
                    logger.info(f"ℹ️ KPI routing failed: {routing_result.get('error')}, falling back to normal flow")
                    next_agent = _determine_next_agent_with_context(state, context_result, validation_result)

            except Exception as e:
                logger.warning(f"⚠️ SmartQueryRouter error: {e}, falling back to normal flow")
                next_agent = _determine_next_agent_with_context(state, context_result, validation_result)
        else:
            # Normal flow for non-KPI queries
            next_agent = _determine_next_agent_with_context(state, context_result, validation_result)

        # Preparar comando para próximo agente
        coordinator_command = CoordinatorCommand(next_agent=next_agent)

        logger.info(f"🎯 Enhanced Coordinator: Routing to {next_agent} (profile: {user_profile}, context confidence: {context_result.confidence_score:.2f})")
        
        return {
            **context_updates,
            "coordinator_command": coordinator_command,
            # Week 4: Profile-aware fields
            "user_profile": user_profile,
            "user_id": user_id,
            "cache_strategy": f"{user_profile.lower()}_optimized",
            "processing_metadata": {
                "coordinator_type": "enhanced_week4",
                "context_processing_time": context_result.processing_time,
                "entities_found": len(context_result.entities_extracted),
                "context_inherited": context_result.context_applied,
                "profile_used": user_profile,
                "profile_detection": state.get("profile_detection_result", {}),
                "smart_router_attempted": is_kpi_query,
                "timestamp": datetime.now().isoformat()
            }
        }
        
    except Exception as e:
        logger.error(f"❌ Enhanced Coordinator error: {e}")

        # Fallback simples - ir para query generator
        return {
            "coordinator_command": CoordinatorCommand("query_generator_agent"),
            "processing_metadata": {
                "coordinator_type": "enhanced_fallback",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
        }


def _determine_next_agent_with_context(
    state: DataHeroState,
    context_result,
    validation_result
) -> str:
    """
    Determina próximo agente considerando contexto conversacional e validação.

    Args:
        state: Estado atual
        context_result: Resultado da preservação de contexto
        validation_result: Resultado da validação de contexto

    Returns:
        Nome do próximo agente
    """

    # Verificar se validação indica problemas críticos
    if not validation_result.is_valid and validation_result.clarification_needed:
        # Por enquanto, continuar com query generator que pode lidar com contexto incompleto
        # Em implementação futura, poderia rotear para agente de esclarecimento
        logger.warning(f"🎯 Context validation issues detected, but proceeding with query generation")
        for issue in validation_result.issues:
            logger.debug(f"   Issue: {issue.message}")

    # Log de validação para debugging
    logger.info(f"🎯 Context validation: valid={validation_result.is_valid}, confidence={validation_result.confidence_score:.2f}")
    # Verificar se já temos SQL query
    if state.get("sql_query"):
        if not state.get("query_result"):
            return "sql_executor"
        elif not state.get("business_analysis"):
            return "business_analyst"
        else:
            return "__end__"
    
    # Verificar se precisamos de validação
    if state.get("sql_query") and not state.get("sql_validated"):
        return "unified_validator"
    
    # Verificar se temos contexto suficiente para gerar query
    if _has_sufficient_context_for_query(state, context_result):
        return "query_generator_agent"
    
    # Verificar se precisamos de esclarecimento de contexto
    if _needs_context_clarification(state, context_result):
        # Por enquanto, vamos para query generator que pode lidar com contexto incompleto
        return "query_generator_agent"
    
    # Fluxo padrão
    return "query_generator_agent"


def _has_sufficient_context_for_query(
    state: DataHeroState, 
    context_result
) -> bool:
    """
    Verifica se há contexto suficiente para gerar query SQL.
    
    Args:
        state: Estado atual
        context_result: Resultado da preservação de contexto
        
    Returns:
        True se há contexto suficiente
    """
    question = state.get("question", "").lower()
    inherited = context_result.inherited_context
    
    # Verificar se a pergunta tem referências que foram resolvidas
    if context_result.resolved_references:
        return True
    
    # Verificar se temos contexto temporal para perguntas que precisam
    temporal_keywords = ["quando", "período", "mês", "ano", "data", "tempo"]
    if any(keyword in question for keyword in temporal_keywords):
        return bool(inherited.get("temporal_filters"))
    
    # Verificar se temos contexto de moeda para perguntas sobre moedas
    currency_keywords = ["dólar", "euro", "usd", "eur", "moeda", "câmbio"]
    if any(keyword in question for keyword in currency_keywords):
        return bool(inherited.get("business_filters", {}).get("business_currency"))
    
    # Para perguntas gerais, sempre temos contexto suficiente
    return True


def _needs_context_clarification(
    state: DataHeroState, 
    context_result
) -> bool:
    """
    Verifica se precisamos de esclarecimento de contexto.
    
    Args:
        state: Estado atual
        context_result: Resultado da preservação de contexto
        
    Returns:
        True se precisamos de esclarecimento
    """
    question = state.get("question", "").lower()
    
    # Verificar referências não resolvidas
    reference_entities = context_result.entities_extracted.get("reference", [])
    unresolved_references = [
        ref for ref in reference_entities 
        if ref.value not in context_result.resolved_references
    ]
    
    if unresolved_references:
        logger.warning(f"🎯 Found unresolved references: {[ref.value for ref in unresolved_references]}")
        # Por enquanto, não bloqueamos - deixamos o query generator lidar
        return False
    
    # Verificar perguntas muito vagas sem contexto
    vague_patterns = ["isso", "aquilo", "anterior", "último", "mesmo"]
    if any(pattern in question for pattern in vague_patterns):
        if not context_result.inherited_context:
            return True
    
    return False


def get_context_for_query_generation(thread_id: str, current_query: str) -> Dict[str, Any]:
    """
    Função utilitária para obter contexto formatado para query generation.
    
    Args:
        thread_id: ID da thread
        current_query: Query atual
        
    Returns:
        Dict com contexto formatado
    """
    return get_context_engine().get_context_for_query_generation(thread_id, current_query)


def clear_thread_context(thread_id: str) -> bool:
    """
    Limpa contexto de uma thread específica.
    
    Args:
        thread_id: ID da thread
        
    Returns:
        True se contexto foi limpo
    """
    return get_context_engine().clear_thread_context(thread_id)


def get_thread_context_summary(thread_id: str) -> Optional[Dict[str, Any]]:
    """
    Obtém resumo do contexto de uma thread.
    
    Args:
        thread_id: ID da thread
        
    Returns:
        Dict com resumo do contexto ou None
    """
    return get_context_engine().get_thread_context_summary(thread_id)


# Função para limpeza periódica de contextos expirados
def cleanup_expired_contexts(max_age_hours: int = 24) -> int:
    """
    Remove contextos de threads expiradas.
    
    Args:
        max_age_hours: Idade máxima em horas
        
    Returns:
        Número de contextos removidos
    """
    return get_context_engine().cleanup_expired_threads(max_age_hours)

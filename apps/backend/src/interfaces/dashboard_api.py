"""
Dashboard API Endpoints - DataHero4
===================================

API endpoints for dashboard functionality including KPI data retrieval.
These endpoints integrate with the existing FastAPI application.
"""

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime

from fastapi import APIRouter, HTTPException, Query, Depends
from pydantic import BaseModel, Field

from src.services.kpi_service import KpiService
from src.services.hybrid_kpi_service import get_hybrid_kpi_service
from src.services.smart_query_router import get_smart_query_router
from src.services.profile_detector import get_profile_detector
from src.config.client_config import CLIENT_CONFIG

logger = logging.getLogger(__name__)

# Create router for dashboard endpoints
dashboard_router = APIRouter(prefix="/api", tags=["dashboard"])


# Response models
class KpiAlert(BaseModel):
    """KPI alert configuration."""
    type: str = Field(..., description="Alert type: 'above' or 'below'")
    threshold: float = Field(..., description="Alert threshold value")
    message: Optional[str] = Field(None, description="Alert message")


class KpiData(BaseModel):
    """KPI data response model."""
    id: str = Field(..., description="KPI identifier")
    title: str = Field(..., description="KPI display name")
    description: str = Field(..., description="KPI description")
    currentValue: float = Field(..., description="Current KPI value")
    format: str = Field(..., description="Value format: currency, percentage, number")
    changePercent: Optional[float] = Field(None, description="Percentage change from previous period")
    trend: str = Field(..., description="Trend direction: up, down, stable")
    chartType: str = Field(..., description="Chart type: line, area, bar")
    chartData: List[Dict[str, Any]] = Field(..., description="Chart data points")
    alert: Optional[KpiAlert] = Field(None, description="Alert configuration")
    isPriority: bool = Field(False, description="Whether KPI is priority")
    order: int = Field(0, description="Display order")
    category: str = Field(..., description="KPI category")
    unit: Optional[str] = Field(None, description="Unit of measurement")
    frequency: Optional[str] = Field(None, description="Update frequency")


class DashboardKpisResponse(BaseModel):
    """Response model for dashboard KPIs endpoint."""
    kpis: List[KpiData] = Field(..., description="List of calculated KPIs")
    total_count: int = Field(..., description="Total number of KPIs")
    sector: str = Field(..., description="Business sector")
    client_id: str = Field(..., description="Client identifier")
    timeframe: str = Field(..., description="Time frame")
    generated_at: datetime = Field(default_factory=datetime.utcnow, description="Response generation timestamp")


class SingleKpiResponse(BaseModel):
    """Response model for single KPI calculation."""
    kpi: KpiData = Field(..., description="Calculated KPI data")
    calculated_at: datetime = Field(default_factory=datetime.utcnow, description="Calculation timestamp")


class DashboardSummaryResponse(BaseModel):
    """Response model for complete dashboard data."""
    kpis: List[KpiData] = Field(..., description="All dashboard KPIs")
    summary: Dict[str, Any] = Field(..., description="Dashboard summary statistics")
    metadata: Dict[str, Any] = Field(..., description="Dashboard metadata")


# Dependency to get KPI service
def get_kpi_service_dependency() -> Any:
    """Dependency to get KPI service instance."""
    return KpiService()


@dashboard_router.get("/dashboard/kpis", response_model=DashboardKpisResponse)
async def get_dashboard_kpis(
    sector: str = Query("cambio", description="Business sector"),
    timeframe: str = Query("1d", description="Time frame for calculations"),
    category: Optional[str] = Query(None, description="Filter by KPI category"),
    priority_only: bool = Query(True, description="Load only priority KPIs for faster response"),
    currency: str = Query("all", description="Currency filter for calculations"),
    kpi_service: Any = Depends(get_kpi_service_dependency)
) -> DashboardKpisResponse:
    """
    Get calculated KPIs for dashboard display.
    
    This endpoint returns a list of calculated KPIs with current values,
    trends, and chart data for dashboard visualization.
    
    **Features:**
    - Lazy loading: KPIs are calculated only when requested
    - Hierarchical caching: Results are cached for performance
    - Real-time data: Values are calculated from current database state
    - Flexible filtering: Filter by category, sector, client
    - Priority loading: Load only critical KPIs first for faster response
    
    **Cache Strategy:**
    - L1 (Memory): 5 minutes
    - L2 (Redis): 1 hour
    - L3 (PostgreSQL): Persistent storage
    
    **Performance:**
    - priority_only=True: ~4-6 KPIs, loads in 10-30 seconds
    - priority_only=False: ~34 KPIs, loads in 3+ minutes
    """
    try:
        logger.info(f"Getting dashboard KPIs for L2M/{sector} (timeframe: {timeframe}, currency: {currency}, priority_only: {priority_only})")

        # Get calculated KPIs from service
        kpis = kpi_service.get_dashboard_kpis(
            sector=sector,
            client_id="L2M",  # Hardcoded - L2M is the DataHero4 client
            timeframe=timeframe,
            category=category,
            priority_only=priority_only,
            currency=currency
        )
        
        # Convert to response models
        kpi_data_list = []
        for kpi in kpis:
            # Convert alert if present
            alert = None
            if kpi.get('alert'):
                alert = KpiAlert(**kpi['alert'])
            
            kpi_data = KpiData(
                id=kpi['id'],
                title=kpi['title'],
                description=kpi['description'],
                currentValue=kpi['currentValue'],
                format=kpi['format'],
                changePercent=kpi.get('changePercent'),
                trend=kpi['trend'],
                chartType=kpi['chartType'],
                chartData=kpi['chartData'],
                alert=alert,
                isPriority=kpi.get('isPriority', False),
                order=kpi.get('order', 0),
                category=kpi['category'],
                unit=kpi.get('unit'),
                frequency=kpi.get('frequency')
            )
            kpi_data_list.append(kpi_data)
        
        response = DashboardKpisResponse(
            kpis=kpi_data_list,
            total_count=len(kpi_data_list),
            sector=sector,
            client_id="L2M",
            timeframe=timeframe
        )
        
        logger.info(f"Returning {len(kpi_data_list)} KPIs for dashboard (priority_only: {priority_only})")
        return response
        
    except Exception as e:
        logger.error(f"Error in get_dashboard_kpis: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to retrieve dashboard KPIs: {str(e)}"
        )


@dashboard_router.get("/kpis/{kpi_id}/calculate", response_model=SingleKpiResponse)
async def calculate_kpi(
    kpi_id: str,
    sector: str = Query("cambio", description="Business sector"),
    client_id: str = Query(CLIENT_CONFIG.SYSTEM_ENTITY, description="Client identifier"),
    kpi_service: Any = Depends(get_kpi_service_dependency)
) -> SingleKpiResponse:
    """
    Calculate a specific KPI on demand.
    
    This endpoint calculates and returns data for a single KPI.
    Useful for lazy loading scenarios where only specific KPIs
    are needed.
    
    **Use Cases:**
    - Lazy loading: Calculate KPI only when user views it
    - Real-time updates: Get fresh calculation for specific KPI
    - Detailed analysis: Focus on single KPI performance
    """
    try:
        logger.info(f"Calculating KPI {kpi_id} for {client_id}/{sector}")
        
        # Calculate single KPI
        kpi_data = kpi_service.calculate_single_kpi(
            kpi_id=kpi_id,
            sector=sector,
            client_id=client_id
        )
        
        if not kpi_data:
            raise HTTPException(
                status_code=404,
                detail=f"KPI '{kpi_id}' not found or inactive"
            )
        
        # Convert alert if present
        alert = None
        if kpi_data.get('alert'):
            alert = KpiAlert(**kpi_data['alert'])
        
        kpi_response = KpiData(
            id=kpi_data['id'],
            title=kpi_data['title'],
            description=kpi_data['description'],
            currentValue=kpi_data['currentValue'],
            format=kpi_data['format'],
            changePercent=kpi_data.get('changePercent'),
            trend=kpi_data['trend'],
            chartType=kpi_data['chartType'],
            chartData=kpi_data['chartData'],
            alert=alert,
            isPriority=kpi_data.get('isPriority', False),
            order=kpi_data.get('order', 0),
            category=kpi_data['category'],
            unit=kpi_data.get('unit'),
            frequency=kpi_data.get('frequency')
        )
        
        response = SingleKpiResponse(kpi=kpi_response)
        
        logger.info(f"Successfully calculated KPI {kpi_id}")
        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error calculating KPI {kpi_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to calculate KPI '{kpi_id}': {str(e)}"
        )


@dashboard_router.get("/kpis/available")
async def get_available_kpis(
    sector: str = Query("cambio", description="Business sector"),
    client_id: str = Query(CLIENT_CONFIG.SYSTEM_ENTITY, description="Client identifier"),
    kpi_service: Any = Depends(get_kpi_service_dependency)
):
    """
    Get list of available KPIs for selection.

    Returns KPI definitions without calculating values for fast loading.
    Perfect for KPI selector interfaces.
    """
    try:
        logger.info(f"📋 Getting available KPIs for {client_id}/{sector}")

        # Get KPI definitions only (no calculations)
        kpi_definitions = kpi_service.get_available_kpis(priority_only=False)

        # Format for frontend selection
        available_kpis = []
        for kpi_def in kpi_definitions:
            available_kpis.append({
                'id': kpi_def.get('id', ''),
                'name': kpi_def.get('name', ''),
                'description': kpi_def.get('description', ''),
                'category': kpi_def.get('category', 'general'),
                'isPriority': kpi_def.get('is_priority', False),
                'format': kpi_def.get('format_type', 'number'),
                'unit': kpi_def.get('unit', ''),
                'frequency': kpi_def.get('frequency', 'daily')
            })

        return {
            'kpis': available_kpis,
            'total_count': len(available_kpis),
            'sector': sector,
            'client_id': client_id
        }

    except Exception as e:
        logger.error(f"Error getting available KPIs: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get available KPIs: {str(e)}")


@dashboard_router.get("/dashboard", response_model=DashboardSummaryResponse)
async def get_dashboard_summary(
    sector: str = Query("cambio", description="Business sector"),
    client_id: str = Query(CLIENT_CONFIG.SYSTEM_ENTITY, description="Client identifier"),
    timeframe: str = Query("1d", description="Time frame for calculations"),
    kpi_service: Any = Depends(get_kpi_service_dependency)
) -> DashboardSummaryResponse:
    """
    Get complete dashboard data including KPIs and summary statistics.
    
    This endpoint provides a comprehensive view of the dashboard
    including all KPIs, summary statistics, and metadata.
    
    **Includes:**
    - All active KPIs with calculated values
    - Summary statistics (totals, averages, trends)
    - Dashboard metadata (last update, data freshness)
    """
    try:
        logger.info(f"Getting complete dashboard for {client_id}/{sector}")
        
        # Get all KPIs
        kpis = kpi_service.get_dashboard_kpis(
            sector=sector,
            client_id=client_id,
            timeframe=timeframe
        )
        
        # Convert to response models
        kpi_data_list = []
        priority_count = 0
        total_value = 0
        
        for kpi in kpis:
            # Convert alert if present
            alert = None
            if kpi.get('alert'):
                alert = KpiAlert(**kpi['alert'])
            
            kpi_data = KpiData(
                id=kpi['id'],
                title=kpi['title'],
                description=kpi['description'],
                currentValue=kpi['currentValue'],
                format=kpi['format'],
                changePercent=kpi.get('changePercent'),
                trend=kpi['trend'],
                chartType=kpi['chartType'],
                chartData=kpi['chartData'],
                alert=alert,
                isPriority=kpi.get('isPriority', False),
                order=kpi.get('order', 0),
                category=kpi['category'],
                unit=kpi.get('unit'),
                frequency=kpi.get('frequency')
            )
            kpi_data_list.append(kpi_data)
            
            if kpi.get('isPriority'):
                priority_count += 1
            
            # Sum currency values for total
            if kpi['format'] == 'currency':
                total_value += kpi['currentValue']
        
        # Generate summary statistics
        summary = {
            'total_kpis': len(kpi_data_list),
            'priority_kpis': priority_count,
            'total_value': total_value,
            'categories': list(set(kpi['category'] for kpi in kpis)),
            'last_calculation': datetime.utcnow().isoformat()
        }
        
        # Generate metadata
        metadata = {
            'sector': sector,
            'client_id': client_id,
            'timeframe': timeframe,
            'data_freshness': 'real-time',
            'cache_enabled': True,
            'version': '1.0'
        }
        
        response = DashboardSummaryResponse(
            kpis=kpi_data_list,
            summary=summary,
            metadata=metadata
        )
        
        logger.info(f"Returning complete dashboard with {len(kpi_data_list)} KPIs")
        return response
        
    except Exception as e:
        logger.error(f"Error in get_dashboard_summary: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to retrieve dashboard summary: {str(e)}"
        )


# === WEEK 4: PERSONALIZED DASHBOARD ENDPOINTS ===

class PersonalizedKpiRequest(BaseModel):
    """Request model for personalized KPIs."""
    user_id: str = Field(..., description="User identifier")
    profile_type: Optional[str] = Field(None, description="User profile type (auto-detected if not provided)")
    timeframe: str = Field(default="week", description="Data timeframe")
    currency: str = Field(default="all", description="Currency filter")
    priority_only: bool = Field(default=False, description="Return only priority KPIs")


def get_hybrid_kpi_service_dep():
    """Dependency to get HybridKpiService instance."""
    return get_hybrid_kpi_service()


def get_smart_router_dep():
    """Dependency to get SmartQueryRouter instance."""
    return get_smart_query_router()


def get_profile_detector_dep():
    """Dependency to get ProfileDetector instance."""
    return get_profile_detector()


@dashboard_router.post("/personalized-kpis", summary="Get personalized KPIs for user")
async def get_personalized_kpis(
    request: PersonalizedKpiRequest,
    hybrid_service: get_hybrid_kpi_service_dep = Depends(),
    router_service: get_smart_router_dep = Depends(),
    profile_service: get_profile_detector_dep = Depends()
):
    """
    Get personalized KPIs based on user profile and preferences.

    **Week 4 Feature**: Routes KPIs through hybrid architecture:
    - Layer 1: Profile-aware snapshots (CEO, CFO)
    - Layer 2: Personalized cache (Trader, Operations)
    - Layer 3: Direct optimized queries (Risk_Manager)

    **Profile Detection**: Automatically detects user profile if not provided
    **Fail-Fast**: No fallbacks to mock data - real data only
    **Performance**: Optimized routing based on user profile characteristics
    """
    try:
        logger.info(f"🎯 Getting personalized KPIs for user {request.user_id}")

        # === PROFILE DETECTION ===
        user_profile = request.profile_type

        if not user_profile:
            logger.info(f"🔍 Auto-detecting profile for user {request.user_id}")
            detection_result = profile_service.detect_profile(request.user_id)

            if detection_result.get('detected_profile') and detection_result.get('confidence', 0) >= 0.30:
                user_profile = detection_result['detected_profile']
                logger.info(f"✅ Profile detected: {user_profile} (confidence: {detection_result['confidence']:.2f})")
            else:
                user_profile = "Operations"  # Default profile
                logger.warning(f"⚠️ Profile detection failed, using default: {user_profile}")

        # === GET PROFILE-SPECIFIC KPIS ===
        # Get recommended KPIs for this profile
        profile_kpis = _get_profile_recommended_kpis(user_profile)

        if request.priority_only:
            # Filter to only priority KPIs
            priority_kpis = ['spread_income_detailed', 'margem_liquida_operacional']
            profile_kpis = [kpi for kpi in profile_kpis if kpi in priority_kpis]

        # === ROUTE THROUGH HYBRID ARCHITECTURE ===
        kpi_results = {}
        routing_metadata = {}

        for kpi_id in profile_kpis:
            try:
                # Route through SmartQueryRouter
                result = router_service.route_kpi_request(
                    kpi_id=kpi_id,
                    client_id="L2M",
                    user_id=request.user_id,
                    timeframe=request.timeframe,
                    currency=request.currency,
                    profile_type=user_profile
                )

                if not result.get('error'):
                    # Convert to dashboard format
                    kpi_data = _convert_hybrid_kpi_to_dashboard_format(result, kpi_id)
                    kpi_results[kpi_id] = kpi_data

                    if result.get('routing_metadata'):
                        routing_metadata[kpi_id] = result['routing_metadata']
                else:
                    logger.warning(f"⚠️ KPI {kpi_id} routing failed: {result.get('error')}")

            except Exception as e:
                logger.error(f"❌ Error routing KPI {kpi_id}: {e}")

        # === RESPONSE ===
        return {
            'user_id': request.user_id,
            'profile_type': user_profile,
            'timeframe': request.timeframe,
            'currency': request.currency,
            'priority_only': request.priority_only,
            'kpis': list(kpi_results.values()),
            'total_kpis': len(kpi_results),
            'routing_metadata': routing_metadata,
            'personalization': {
                'profile_detected': not bool(request.profile_type),
                'recommended_kpis': profile_kpis,
                'cache_strategy': _get_profile_cache_strategy(user_profile),
                'routing_layer': _get_profile_routing_layer(user_profile)
            }
        }

    except Exception as e:
        logger.error(f"❌ Error getting personalized KPIs for user {request.user_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                'error': 'personalized_kpis_error',
                'message': str(e),
                'user_id': request.user_id
            }
        )


@dashboard_router.get("/personalized-kpis/{user_id}/recommendations", summary="Get KPI recommendations for user")
async def get_kpi_recommendations(
    user_id: str,
    profile_service: get_profile_detector_dep = Depends()
):
    """
    Get KPI recommendations based on user profile.

    Returns recommended KPIs without calculating values,
    useful for dashboard configuration and setup.
    """
    try:
        logger.info(f"📋 Getting KPI recommendations for user {user_id}")

        # Detect profile
        detection_result = profile_service.detect_profile(user_id)

        profile_type = "Operations"  # Default
        if detection_result.get('detected_profile') and detection_result.get('confidence', 0) >= 0.30:
            profile_type = detection_result['detected_profile']

        # Get recommendations
        recommended_kpis = _get_profile_recommended_kpis(profile_type)

        return {
            'user_id': user_id,
            'detected_profile': profile_type,
            'confidence': detection_result.get('confidence', 0.0),
            'recommended_kpis': [
                {
                    'id': kpi_id,
                    'name': _get_kpi_display_name(kpi_id),
                    'description': _get_kpi_description(kpi_id),
                    'category': _get_kpi_category(kpi_id),
                    'priority': kpi_id in ['spread_income_detailed', 'margem_liquida_operacional']
                }
                for kpi_id in recommended_kpis
            ],
            'profile_characteristics': {
                'cache_ttl': _get_profile_cache_ttl(profile_type),
                'preferred_layer': _get_profile_routing_layer(profile_type),
                'update_frequency': _get_profile_update_frequency(profile_type)
            }
        }

    except Exception as e:
        logger.error(f"❌ Error getting KPI recommendations for user {user_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                'error': 'kpi_recommendations_error',
                'message': str(e),
                'user_id': user_id
            }
        )


# === HELPER FUNCTIONS ===

def _get_profile_recommended_kpis(profile_type: str) -> List[str]:
    """Get recommended KPIs for a profile type."""
    recommendations = {
        'CEO': ['spread_income_detailed', 'margem_liquida_operacional'],
        'CFO': ['margem_liquida_operacional', 'custo_por_transacao'],
        'Risk_Manager': ['tempo_processamento_medio'],
        'Trader': ['spread_income_detailed', 'tempo_processamento_medio'],
        'Operations': ['custo_por_transacao', 'tempo_processamento_medio']
    }
    return recommendations.get(profile_type, ['custo_por_transacao', 'tempo_processamento_medio'])


def _convert_hybrid_kpi_to_dashboard_format(hybrid_result: Dict[str, Any], kpi_id: str) -> Dict[str, Any]:
    """Convert hybrid KPI result to dashboard format."""
    return {
        'id': kpi_id,
        'title': hybrid_result.get('title', _get_kpi_display_name(kpi_id)),
        'description': hybrid_result.get('description', _get_kpi_description(kpi_id)),
        'currentValue': hybrid_result.get('currentValue', 0),
        'format': _get_kpi_format(kpi_id),
        'changePercent': None,  # Would need historical data
        'trend': 'stable',  # Would need trend calculation
        'chartType': _get_kpi_chart_type(kpi_id),
        'chartData': _generate_mock_chart_data(hybrid_result.get('currentValue', 0)),
        'alert': None,
        'isPriority': kpi_id in ['spread_income_detailed', 'margem_liquida_operacional'],
        'order': _get_kpi_order(kpi_id),
        'category': _get_kpi_category(kpi_id),
        'unit': hybrid_result.get('unit', ''),
        'frequency': 'realtime',
        'source': hybrid_result.get('source', 'hybrid'),
        'metadata': hybrid_result.get('metadata', {})
    }


def _get_kpi_display_name(kpi_id: str) -> str:
    """Get display name for KPI."""
    names = {
        'spread_income_detailed': 'Spread Income Detalhado',
        'margem_liquida_operacional': 'Margem Líquida Operacional',
        'custo_por_transacao': 'Custo por Transação',
        'tempo_processamento_medio': 'Tempo Processamento Médio'
    }
    return names.get(kpi_id, kpi_id.replace('_', ' ').title())


def _get_kpi_description(kpi_id: str) -> str:
    """Get description for KPI."""
    descriptions = {
        'spread_income_detailed': 'Receita detalhada por spread por moeda e período',
        'margem_liquida_operacional': 'Margem operacional líquida: (Receita Spread - Custos Operacionais) / Receita Total * 100',
        'custo_por_transacao': 'Custo operacional médio por transação processada',
        'tempo_processamento_medio': 'Tempo médio de processamento de transações (em segundos)'
    }
    return descriptions.get(kpi_id, f'KPI: {kpi_id}')


def _get_kpi_category(kpi_id: str) -> str:
    """Get category for KPI."""
    categories = {
        'spread_income_detailed': 'spread',
        'margem_liquida_operacional': 'performance',
        'custo_por_transacao': 'performance',
        'tempo_processamento_medio': 'performance'
    }
    return categories.get(kpi_id, 'general')


def _get_kpi_format(kpi_id: str) -> str:
    """Get format for KPI."""
    formats = {
        'spread_income_detailed': 'currency',
        'margem_liquida_operacional': 'percentage',
        'custo_por_transacao': 'currency',
        'tempo_processamento_medio': 'number'
    }
    return formats.get(kpi_id, 'number')


def _get_kpi_chart_type(kpi_id: str) -> str:
    """Get chart type for KPI."""
    chart_types = {
        'spread_income_detailed': 'area',
        'margem_liquida_operacional': 'line',
        'custo_por_transacao': 'bar',
        'tempo_processamento_medio': 'line'
    }
    return chart_types.get(kpi_id, 'line')


def _get_kpi_order(kpi_id: str) -> int:
    """Get display order for KPI."""
    orders = {
        'spread_income_detailed': 1,
        'margem_liquida_operacional': 2,
        'custo_por_transacao': 3,
        'tempo_processamento_medio': 4
    }
    return orders.get(kpi_id, 99)


def _generate_mock_chart_data(current_value: float) -> List[Dict[str, Any]]:
    """Generate mock chart data for visualization."""
    # Simple mock data - in production this would come from historical data
    return [
        {'date': '2025-01-15', 'value': current_value * 0.9},
        {'date': '2025-01-16', 'value': current_value * 0.95},
        {'date': '2025-01-17', 'value': current_value * 1.1},
        {'date': '2025-01-18', 'value': current_value * 1.05},
        {'date': '2025-01-19', 'value': current_value * 0.98},
        {'date': '2025-01-20', 'value': current_value * 1.02},
        {'date': '2025-01-21', 'value': current_value}
    ]


def _get_profile_cache_strategy(profile_type: str) -> Dict[str, Any]:
    """Get cache strategy for profile."""
    strategies = {
        'CEO': {'ttl': 3600, 'layer': 'snapshot', 'priority': 'accuracy'},
        'CFO': {'ttl': 1800, 'layer': 'snapshot', 'priority': 'accuracy'},
        'Risk_Manager': {'ttl': 300, 'layer': 'direct', 'priority': 'real_time'},
        'Trader': {'ttl': 60, 'layer': 'cache', 'priority': 'speed'},
        'Operations': {'ttl': 900, 'layer': 'cache', 'priority': 'efficiency'}
    }
    return strategies.get(profile_type, {'ttl': 900, 'layer': 'cache', 'priority': 'efficiency'})


def _get_profile_routing_layer(profile_type: str) -> str:
    """Get preferred routing layer for profile."""
    layers = {
        'CEO': 'snapshot',
        'CFO': 'snapshot',
        'Risk_Manager': 'direct',
        'Trader': 'cache',
        'Operations': 'cache'
    }
    return layers.get(profile_type, 'cache')


def _get_profile_cache_ttl(profile_type: str) -> int:
    """Get cache TTL for profile."""
    ttls = {
        'CEO': 3600,      # 1 hour
        'CFO': 1800,      # 30 minutes
        'Risk_Manager': 300,  # 5 minutes
        'Trader': 60,     # 1 minute
        'Operations': 900  # 15 minutes
    }
    return ttls.get(profile_type, 900)


def _get_profile_update_frequency(profile_type: str) -> str:
    """Get update frequency for profile."""
    frequencies = {
        'CEO': 'hourly',
        'CFO': 'every_30_minutes',
        'Risk_Manager': 'every_5_minutes',
        'Trader': 'realtime',
        'Operations': 'every_15_minutes'
    }
    return frequencies.get(profile_type, 'every_15_minutes')

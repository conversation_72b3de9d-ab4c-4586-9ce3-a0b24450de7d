"""DataHero State definition for LangGraph workflow."""

from typing import TypedDict, Dict, Any, List, Optional, Union
from datetime import datetime


class EnhancedContext(TypedDict):
    """Enhanced context that combines static and dynamic knowledge."""
    schema: str
    kpis: Dict[str, Any]
    similar_successful_queries: List[Dict[str, Any]]
    learned_correction_patterns: List[Dict[str, Any]]
    domain_entities: Dict[str, Any]
    confidence_boost: float
    relevance_scores: Dict[str, float]


class DataHeroState(TypedDict, total=False):
    """Central state definition for DataHero LangGraph workflow.

    This TypedDict defines all possible fields that can exist in the
    workflow state. Using total=False allows fields to be optional.
    """

    # === Input Fields ===
    question: str
    client_id: str
    sector: str
    channel: str  # "api", "cli", "whatsapp", etc.

    # === Week 4: Profile-Aware Fields (REQUIRED) ===
    user_profile: str  # REQUIRED - fail fast if None
    user_id: str  # REQUIRED - for profile detection and routing

    # === Week 4: Hybrid Architecture Fields ===
    personalized_kpis: Optional[List[Dict[str, Any]]]  # KPIs filtered by profile
    cache_strategy: Optional[str]  # Cache strategy per profile
    routing_metadata: Optional[Dict[str, Any]]  # SmartQueryRouter metadata
    profile_detection_result: Optional[Dict[str, Any]]  # Profile detection info
    hybrid_kpi_results: Optional[Dict[str, Any]]  # Results from HybridKpiService

    # === Processing Fields ===
    # Domain extraction
    extracted_entities: Dict[str, Any]
    entity_confidence: float
    
    # Context enhancement
    enhanced_context: EnhancedContext
    context_tokens: int
    
    # Cache lookup
    cache_hit: bool
    cache_source: Optional[str]  # "postgresql", etc.
    cache_lookup: Optional[Dict[str, Any]]  # Full cache lookup result
    cached_query: Optional[Dict[str, Any]]  # Cached query details
    cache_confidence: Optional[float]  # Original cache confidence
    similar_queries: List[Dict[str, Any]]
    
    # Query generation
    sql_query: str
    query_generation_attempts: int
    generation_method: str  # "llm", "cache", "pattern"
    
    # Validation
    query_valid: bool
    sql_valid: bool  # CRITICAL FIX: Added missing field for SQL validation status
    validation_errors: List[str]
    validation_warnings: List[str]
    validation_confidence: float  # CRITICAL FIX: Added missing field for validation confidence
    validation_type: Optional[str]  # CRITICAL FIX: Added missing field for validation type (e.g., "cached", "llm")
    validation_suggestions: List[str]  # CRITICAL FIX: Added missing field for validation suggestions
    semantic_validation_passed: bool
    
    # === Results Fields ===
    results: Optional[Union[List[Dict], str]]
    query_result: Optional[Union[List[Dict], str]]  # CRITICAL FIX: Added missing field
    result_count: int
    execution_time: float
    error: Optional[str]
    error_type: Optional[str]  # "validation", "execution", "timeout", etc.
    
    # === Learning Fields ===
    confidence_score: float
    feedback_applied: List[Dict[str, Any]]
    patterns_matched: List[str]
    learning_updated: bool
    cache_size: int
    pattern_count: int
    
    # === Feedback Fields ===
    feedback_data: Optional[Dict[str, Any]]
    feedback_processed: bool
    patterns_learned: int
    cache_updated: bool

    # === Reprocessing Fields ===
    feedback_context: Optional[Dict[str, Any]]  # Context from user feedback for reprocessing
    reprocessing_mode: bool  # True when reprocessing with feedback
    feedback_id: Optional[str]  # ID of the feedback being processed
    original_query_id: Optional[str]  # Original query ID that received feedback
    feedback_analyzed: bool  # True when feedback has been analyzed
    correction_insights: Optional[Dict[str, Any]]  # Insights from feedback analysis
    avoid_patterns: Optional[List[str]]  # Patterns to avoid based on feedback
    correction_instructions: Optional[str]  # Instructions for LLM based on feedback
    
    # === Presentation Fields ===
    formatted_response: str
    insights: List[str]
    suggestions: List[str]
    visualization_data: Optional[Dict[str, Any]]
    business_analysis: Optional[Dict[str, Any]]

    # === Cache Fields ===
    cached_business_analysis: Optional[Dict[str, Any]]
    cached_visualization_data: Optional[Dict[str, Any]]
    _cached_business_analysis_backup: Optional[Dict[str, Any]]
    _cached_visualization_data_backup: Optional[Dict[str, Any]]
    
    # === Metadata Fields ===
    timestamp: datetime
    query_id: str
    session_id: Optional[str]
    tokens_used: int
    llm_calls: int
    total_cost: float
    
    # === Routing Control Fields ===
    next_action: Optional[str]
    retry_count: int
    max_retries: int
    should_use_cache: bool
    force_regeneration: bool
    coordinator_iterations: int  # CIRCUIT BREAKER: Track coordinator loops
    
    # === Debug/Monitoring Fields ===
    _source: str  # "legacy_pipeline" or "langgraph_pipeline"
    _debug_info: Dict[str, Any]
    _performance_metrics: Dict[str, float]
    
    # === Feature Flags ===
    enable_learning: bool
    enable_feedback: bool
    enable_cache: bool
    enable_patterns: bool
    
    # === Configuration Fields ===
    schema: Dict[str, Any]  # Complete database schema
    schema_relevance_path: str
    sector_kpi_path: str
    llm_config_path: str
    prompt_path: str
    prompt_path_business_analyst: str
    prompt_path_insight_generator: str
    prompt_path_question_suggester: str
    thread_id: str
    categorical_values: Dict[str, Any]
    categorical_values_path: str
    messages: List[Any]
    kpi_context: Dict[str, Any]
    attempts: int
    user_role: Optional[str]

    # === Conversational Chat Fields ===
    # Thread and conversation management
    conversation_context: Optional[Dict[str, Any]]  # Full conversation context
    conversation_messages: Optional[List[Dict[str, str]]]  # Formatted messages for LLM
    current_message_id: Optional[str]  # ID of current message being processed
    is_conversational: bool  # Flag to indicate conversational mode

    # Message history and context
    message_history: Optional[List[Dict[str, Any]]]  # Raw message history
    context_window: Optional[Dict[str, Any]]  # Optimized context window
    context_tokens_used: Optional[int]  # Tokens used in context window
    context_optimization_strategy: Optional[str]  # Strategy used for context optimization

    # === Context Preservation Fields ===
    # Extracted entities from current query
    extracted_entities: Optional[Dict[str, Any]]  # Entities extracted by ConversationalEntityExtractor

    # Inherited context from previous queries
    inherited_context: Optional[Dict[str, Any]]  # Context inherited from conversation history
    temporal_context: Optional[Dict[str, Any]]  # Active temporal filters (dates, periods)
    business_context: Optional[Dict[str, Any]]  # Active business filters (currencies, clients)
    filter_context: Optional[Dict[str, Any]]  # Active filter conditions

    # Reference resolution
    resolved_references: Optional[Dict[str, Any]]  # Resolved pronomial references
    reference_map: Optional[Dict[str, Any]]  # Map of references to resolved values

    # Context preservation metadata
    context_preservation_result: Optional[Dict[str, Any]]  # Full result from ContextPreservationEngine
    context_confidence_score: Optional[float]  # Confidence score for context preservation
    context_applied: Optional[bool]  # Whether context was successfully applied

    # Streaming and real-time features
    streaming_enabled: bool  # Whether response should be streamed
    stream_message_id: Optional[str]  # Message ID for streaming session
    stream_status: Optional[str]  # Current streaming status

    # Conversation metadata
    conversation_title: Optional[str]  # Title of the conversation
    conversation_created_at: Optional[str]  # ISO timestamp of conversation creation
    conversation_updated_at: Optional[str]  # ISO timestamp of last update
    conversation_message_count: Optional[int]  # Total messages in conversation

    # User and access control
    conversation_user_id: Optional[str]  # User who owns the conversation
    conversation_permissions: Optional[Dict[str, bool]]  # User permissions for conversation

    # Integration with existing feedback system
    conversation_feedback_enabled: bool  # Whether feedback is enabled for this conversation
    message_feedback_data: Optional[Dict[str, Any]]  # Feedback data for current message

    # Performance and optimization
    conversation_performance_metrics: Optional[Dict[str, float]]  # Performance metrics for conversation
    context_compression_ratio: Optional[float]  # Ratio of context compression applied
    relevance_threshold: Optional[float]  # Threshold used for message relevance filtering
{"description": "Patterns compostos para perguntas multi-objetivo específicas do domínio de câmbio", "version": "2.0", "domain": "cambio", "force_sql_output": true, "extract_base_query_only": true, "disable_json_templates": true, "prefer_simple_sql": true, "patterns": {"vendas_e_ranking_clientes": {"pattern_id": "vendas_ranking_clientes_v2", "description": "Pattern para pergunta teste: vendas + ranking de clientes", "triggers": ["quanto vendemos.*clientes", "vendas.*quais clientes", "volume.*principais clientes", "clientes.*mais.*vendas"], "intents_required": ["volume", "ranking", "entity"], "entity_focus": "clientes", "sql_template": {"base_query": "WITH vendas_por_cliente_moeda AS (SELECT p.nome, p.id_pessoa, bm.simbolo as moeda, SUM(b.valor_me) as total_vendas FROM boleta b JOIN pessoa p ON b.id_cliente = p.id_pessoa JOIN boleta_moeda bm ON b.id_moeda = bm.id WHERE {temporal_filter} AND {currency_filter} AND b.tipo_operacao = 'VENDA' GROUP BY p.id_pessoa, p.nome, bm.simbolo)", "ranking_query": "SELECT nome, moeda, total_vendas, ROW_NUMBER() OVER (PARTITION BY moeda ORDER BY total_vendas DESC) as ranking FROM vendas_por_cliente_moeda WHERE ranking <= {limit_clause}", "parameters": {"temporal_filter": "EXTRACT(YEAR FROM b.data_operacao) = {year}", "currency_filter": "bm.simbolo IN ({currencies})", "limit_clause": "{top_n}"}}, "required_tables": ["boleta", "pessoa", "moeda"], "required_columns": ["id_cliente", "nome", "valor_me", "simbolo", "data_operacao", "tipo_operacao"], "confidence_boost": 0.95, "priority": 1}, "volume_e_detalhamento": {"pattern_id": "volume_detalhamento_v2", "description": "Pattern para análises de volume com detalhamento por múltiplas dimensões", "triggers": ["volume.*incluindo", "valor.*detalhes", "total.*especificar", "montante.*informar"], "intents_required": ["volume", "metric", "aggregation"], "sql_template": {"base_query": "WITH analise_volume AS (SELECT {group_columns}, SUM({value_column}) as volume_total, COUNT(*) as qtd_operacoes, AVG({value_column}) as valor_medio FROM {main_table} {joins} WHERE {filters} GROUP BY {group_columns})", "detail_query": "SELECT *, ROUND((volume_total / SUM(volume_total) OVER()) * 100, 2) as percentual FROM analise_volume ORDER BY volume_total DESC {limit_clause}", "parameters": {"group_columns": "{entity_name_column}", "value_column": "valor_operacao", "main_table": "boleta b", "joins": "JOIN {entity_table} e ON b.{foreign_key} = e.{entity_id}", "filters": "{temporal_filter} AND {additional_filters}", "limit_clause": "LIMIT {top_n}"}}, "required_tables": ["boleta"], "required_columns": ["valor_operacao", "data_operacao"], "confidence_boost": 0.85, "priority": 2}, "filtro_threshold_agregacao": {"pattern_id": "filtro_threshold_v2", "description": "Pattern para filtros por valor com agregação e agrupamento", "triggers": ["superior.*total", "acima.*valor.*cada", "maior.*montante.*por", "threshold.*agrupado"], "intents_required": ["filter", "aggregation", "entity"], "sql_template": {"base_query": "WITH operacoes_filtradas AS (SELECT {entity_name}, {entity_id}, SUM({value_column}) as total_por_entidade FROM {main_table} {joins} WHERE {temporal_filter} AND {value_column} > {threshold} GROUP BY {entity_id}, {entity_name} HAVING SUM({value_column}) > {threshold})", "result_query": "SELECT {entity_name}, total_por_entidade, COUNT(*) OVER() as total_entidades FROM operacoes_filtradas ORDER BY total_por_entidade DESC", "parameters": {"value_column": "valor_operacao", "main_table": "boleta b", "joins": "JOIN pessoa p ON b.id_cliente = p.id_pessoa", "entity_name": "p.nome", "entity_id": "p.id_pessoa", "threshold": "{filter_value}", "temporal_filter": "{temporal_condition}"}}, "required_tables": ["boleta", "pessoa"], "required_columns": ["valor_operacao", "id_cliente", "nome", "data_operacao"], "confidence_boost": 0.88, "priority": 2}, "ranking_multi_moedas": {"pattern_id": "ranking_multi_moedas_v2", "description": "Pattern para ranking com múltiplas moedas especificadas", "triggers": ["euro.*usd.*dolar", "moedas.*ranking", "eur.*brl.*volume", "principais.*moedas.*valor"], "intents_required": ["ranking", "entity", "volume"], "entity_focus": "moedas", "sql_template": {"base_query": "WITH volume_por_moeda AS (SELECT bm.moeda as nome_moeda, bm.simbolo as moeda_codigo, SUM(b.valor_me) as volume_total, COUNT(DISTINCT b.id_boleta) as qtd_operacoes FROM boleta b JOIN boleta_moeda bm ON b.id_moeda = bm.id WHERE {temporal_filter} {currency_filter} AND b.tipo_operacao = 'VENDA' GROUP BY bm.simbolo, bm.moeda)", "ranking_query": "SELECT nome_moeda, moeda_codigo, volume_total, qtd_operacoes, DENSE_RANK() OVER (ORDER BY volume_total DESC) as ranking FROM volume_por_moeda ORDER BY volume_total DESC {limit_clause}", "parameters": {"temporal_filter": "EXTRACT(YEAR FROM b.data_operacao) = {year}", "currency_filter": "AND bm.simbolo IN ({currency_list})", "limit_clause": "LIMIT {top_n}"}}, "required_tables": ["boleta", "moeda"], "required_columns": ["simbolo", "valor_me", "moeda", "data_operacao", "tipo_operacao"], "confidence_boost": 0.92, "priority": 1}, "temporal_negacao_historico": {"pattern_id": "temporal_negacao_v2", "description": "Pattern para análises com negação temporal e histórico", "triggers": ["não.*realizaram.*mas.*foram", "que.*não.*últimos.*anteriormente", "inativos.*histórico.*período", "sem.*movimento.*mas.*cliente"], "intents_required": ["negation", "temporal", "entity"], "sql_template": {"main_query": "WITH clientes_inativos AS (SELECT DISTINCT p.id_pessoa, p.nome FROM pessoa p WHERE p.id_pessoa NOT IN (SELECT DISTINCT b.id_cliente FROM boleta b WHERE {recent_period_filter}) AND p.id_pessoa IN (SELECT DISTINCT b2.id_cliente FROM boleta b2 WHERE {historical_period_filter}))", "detail_query": "SELECT ci.nome, COALESCE(SUM(b.valor_operacao), 0) as valor_historico_total, COUNT(b.id_boleta) as operacoes_historicas FROM clientes_inativos ci LEFT JOIN boleta b ON ci.id_pessoa = b.id_cliente AND {historical_period_filter} GROUP BY ci.id_pessoa, ci.nome ORDER BY valor_historico_total DESC", "parameters": {"recent_period_filter": "b.data_operacao >= CURRENT_DATE - INTERVAL '{recent_months} months'", "historical_period_filter": "b2.data_operacao < CURRENT_DATE - INTERVAL '{recent_months} months'"}}, "required_tables": ["boleta", "pessoa"], "required_columns": ["id_cliente", "nome", "valor_operacao", "data_operacao"], "confidence_boost": 0.85, "priority": 3}}, "pattern_combinations": {"vendas_ranking_temporal": {"description": "Combinação de vendas + ranking + filtro temporal", "component_patterns": ["vendas_e_ranking_clientes", "filtro_threshold_agregacao"], "merge_strategy": "intersection", "confidence_multiplier": 1.1}, "multi_moeda_volume": {"description": "Combinação de múltiplas moedas + volume + ranking", "component_patterns": ["ranking_multi_moedas", "volume_e_detalhamento"], "merge_strategy": "union", "confidence_multiplier": 1.05}}, "validation_rules": {"required_entities": {"clientes": ["pessoa", "boleta"], "moedas": ["moeda"], "operacoes": ["boleta"], "parceiros": ["banco", "boleta"]}, "mandatory_joins": {"boleta_pessoa": "JOIN pessoa p ON b.id_cliente = p.id_pessoa", "boleta_moeda": "JOIN moeda m ON b.id_moeda = m.id", "moeda_info": "JOIN moeda m ON b.id_moeda = m.id"}}, "optimization_hints": {"performance": {"use_indexes": ["data_operacao", "id_cliente", "moeda_codigo"], "partition_by_date": true, "cache_frequent_patterns": ["vendas_e_ranking_clientes", "ranking_multi_moedas"]}, "precision": {"validate_fks": true, "check_data_types": true, "enforce_constraints": true}}}
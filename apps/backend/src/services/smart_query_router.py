"""
Smart Query Router for DataHero4 Hybrid Architecture
====================================================

Intelligent 3-layer routing system that integrates existing services:
- Layer 1: Profile-aware snapshots (ProfileAwareSnapshotService)
- Layer 2: Personalized cache (PersonalizedCacheSystem)  
- Layer 3: Direct optimized queries (KpiRepository)

Features:
- Fail-fast routing without fallbacks between layers
- Profile-based routing decisions
- Performance optimization per user profile
- Integration with existing DataHero4 services

Author: DataHero4 Team
Date: 2025-01-21
"""

import logging
import time
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, date
from enum import Enum

from src.services.profile_aware_snapshot_service import ProfileAwareSnapshotService
from src.caching.personalized_cache_system import get_personalized_cache
from src.services.profile_detector import get_profile_detector
from src.services.kpi_service import KpiService
from src.services.banco_central_api import get_banco_central_api
from src.utils.learning_db_utils import get_db_manager

logger = logging.getLogger(__name__)


class QueryLayer(Enum):
    """Query routing layers."""
    SNAPSHOT = "snapshot"
    CACHE = "cache"
    DIRECT = "direct"


class SmartQueryRouter:
    """
    Intelligent query router for hybrid architecture.
    
    Routes KPI requests through 3 optimized layers based on user profile,
    data criticality, and performance requirements.
    """
    
    def __init__(self):
        """Initialize SmartQueryRouter with integrated services."""
        self.snapshot_service = ProfileAwareSnapshotService()
        self.cache_system = get_personalized_cache()
        self.profile_detector = get_profile_detector()
        self.kpi_service = KpiService()
        self.bcb_api = get_banco_central_api()  # Week 4: BCB integration
        
        # Routing strategy by profile
        self.routing_strategy = {
            'CEO': {
                'preferred_layer': QueryLayer.SNAPSHOT,
                'cache_fallback': True,
                'direct_fallback': False,  # CEOs prefer pre-calculated data
                'max_wait_time': 10.0,  # 10 seconds max
                'critical_kpis': ['spread_income_detailed', 'margem_liquida_operacional']
            },
            'CFO': {
                'preferred_layer': QueryLayer.SNAPSHOT,
                'cache_fallback': True,
                'direct_fallback': True,  # CFOs accept real-time for accuracy
                'max_wait_time': 15.0,  # 15 seconds max
                'critical_kpis': ['margem_liquida_operacional', 'custo_por_transacao']
            },
            'Risk_Manager': {
                'preferred_layer': QueryLayer.DIRECT,  # Risk needs real-time
                'cache_fallback': True,
                'direct_fallback': False,  # Already direct
                'max_wait_time': 5.0,   # 5 seconds max - risk is time-critical
                'critical_kpis': ['var_diario', 'exposicao_cambial']
            },
            'Trader': {
                'preferred_layer': QueryLayer.CACHE,  # Traders need speed
                'cache_fallback': False,  # Cache or fail fast
                'direct_fallback': True,  # Accept direct if cache miss
                'max_wait_time': 2.0,   # 2 seconds max - trading is time-critical
                'critical_kpis': ['spread_realtime', 'volume_hora']
            },
            'Operations': {
                'preferred_layer': QueryLayer.CACHE,
                'cache_fallback': True,
                'direct_fallback': True,
                'max_wait_time': 8.0,   # 8 seconds max
                'critical_kpis': ['tempo_processamento_medio', 'custo_por_transacao']
            }
        }
        
        logger.info("✅ SmartQueryRouter initialized with hybrid architecture")
    
    def route_kpi_request(
        self,
        kpi_id: str,
        client_id: str,
        user_id: str,
        timeframe: str = "week",
        currency: str = "all",
        profile_type: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Route KPI request through optimal layer based on profile and requirements.
        
        Args:
            kpi_id: KPI identifier
            client_id: Client identifier
            user_id: User identifier
            timeframe: Data timeframe
            currency: Currency filter
            profile_type: User profile (will be detected if not provided)
            
        Returns:
            KPI result with routing metadata
        """
        start_time = time.time()
        
        logger.info(f"🚀 Routing KPI request: {kpi_id} for user {user_id} profile {profile_type}")
        
        try:
            # Detect profile if not provided
            if not profile_type:
                profile_detection = self.profile_detector.detect_profile(user_id)
                profile_type = profile_detection.get('detected_profile')
                
                if not profile_type:
                    logger.error(f"❌ Cannot route request without valid profile for user {user_id}")
                    return {
                        'error': 'profile_detection_failed',
                        'message': 'Cannot route KPI request without valid user profile',
                        'user_id': user_id,
                        'kpi_id': kpi_id,
                        'routing_time_ms': (time.time() - start_time) * 1000
                    }
            
            # Get routing strategy for profile
            strategy = self.routing_strategy.get(profile_type, self.routing_strategy['Operations'])
            
            # Determine routing layers to try
            routing_layers = self._determine_routing_layers(strategy, kpi_id)
            
            # Route through layers
            result = None
            used_layer = None
            layer_attempts = []
            
            for layer in routing_layers:
                layer_start = time.time()
                
                try:
                    if layer == QueryLayer.SNAPSHOT:
                        result = self._route_to_snapshot(
                            kpi_id, client_id, user_id, profile_type, timeframe
                        )
                    elif layer == QueryLayer.CACHE:
                        result = self._route_to_cache(
                            kpi_id, client_id, user_id, profile_type, timeframe, currency
                        )
                    elif layer == QueryLayer.DIRECT:
                        result = self._route_to_direct(
                            kpi_id, client_id, user_id, profile_type, timeframe, currency
                        )
                    
                    layer_time = (time.time() - layer_start) * 1000
                    layer_attempts.append({
                        'layer': layer.value,
                        'time_ms': layer_time,
                        'success': result is not None
                    })
                    
                    if result:
                        used_layer = layer
                        logger.info(f"✅ KPI {kpi_id} routed via {layer.value} layer ({layer_time:.1f}ms)")
                        break
                    
                    # Check timeout
                    if (time.time() - start_time) > strategy['max_wait_time']:
                        logger.warning(f"⏰ Routing timeout for KPI {kpi_id} after {strategy['max_wait_time']}s")
                        break
                        
                except Exception as layer_error:
                    layer_time = (time.time() - layer_start) * 1000
                    layer_attempts.append({
                        'layer': layer.value,
                        'time_ms': layer_time,
                        'success': False,
                        'error': str(layer_error)
                    })
                    logger.error(f"❌ Error in {layer.value} layer for KPI {kpi_id}: {layer_error}")
            
            # Prepare response
            total_time = (time.time() - start_time) * 1000
            
            if result:
                # Success - add routing metadata
                result['routing_metadata'] = {
                    'user_id': user_id,
                    'profile_type': profile_type,
                    'used_layer': used_layer.value,
                    'routing_time_ms': total_time,
                    'layer_attempts': layer_attempts,
                    'strategy': strategy,
                    'routed_at': datetime.now().isoformat()
                }
                
                # Cache result if not from cache layer
                if used_layer != QueryLayer.CACHE:
                    self._cache_result(kpi_id, client_id, user_id, profile_type, timeframe, currency, result)
                
                return result
            else:
                # Fail fast - no fallbacks worked
                logger.error(f"❌ All routing layers failed for KPI {kpi_id} user {user_id}")
                return {
                    'error': 'routing_failed',
                    'message': f'All routing layers failed for KPI {kpi_id}',
                    'user_id': user_id,
                    'profile_type': profile_type,
                    'kpi_id': kpi_id,
                    'routing_time_ms': total_time,
                    'layer_attempts': layer_attempts,
                    'failed_at': datetime.now().isoformat()
                }
                
        except Exception as e:
            total_time = (time.time() - start_time) * 1000
            logger.error(f"❌ Critical error in SmartQueryRouter: {e}")
            return {
                'error': 'router_error',
                'message': str(e),
                'user_id': user_id,
                'kpi_id': kpi_id,
                'routing_time_ms': total_time
            }
    
    def _determine_routing_layers(self, strategy: Dict[str, Any], kpi_id: str) -> List[QueryLayer]:
        """Determine which layers to try based on strategy."""
        layers = [strategy['preferred_layer']]
        
        # Add fallback layers based on strategy
        if strategy.get('cache_fallback') and strategy['preferred_layer'] != QueryLayer.CACHE:
            layers.append(QueryLayer.CACHE)
        
        if strategy.get('direct_fallback') and strategy['preferred_layer'] != QueryLayer.DIRECT:
            layers.append(QueryLayer.DIRECT)
        
        # For critical KPIs, always try snapshot first if available
        if kpi_id in strategy.get('critical_kpis', []) and QueryLayer.SNAPSHOT not in layers:
            layers.insert(0, QueryLayer.SNAPSHOT)
        
        return layers
    
    def _route_to_snapshot(
        self, 
        kpi_id: str, 
        client_id: str, 
        user_id: str, 
        profile_type: str, 
        timeframe: str
    ) -> Optional[Dict[str, Any]]:
        """Route to snapshot layer."""
        try:
            # Get profile snapshot
            snapshot = self.snapshot_service.get_profile_snapshot(
                client_id=client_id,
                profile_type=profile_type
            )
            
            if snapshot and kpi_id in snapshot.get('kpis', {}):
                kpi_data = snapshot['kpis'][kpi_id]
                
                return {
                    'kpi_id': kpi_id,
                    'currentValue': kpi_data['value'],
                    'formattedValue': kpi_data['formatted'],
                    'title': kpi_data['title'],
                    'description': kpi_data['description'],
                    'source': 'snapshot',
                    'profile_context': kpi_data.get('profile_context', {}),
                    'snapshot_date': snapshot.get('period_date'),
                    'timeframe': timeframe
                }
            
            return None
            
        except Exception as e:
            logger.error(f"❌ Snapshot layer error for KPI {kpi_id}: {e}")
            return None
    
    def _route_to_cache(
        self, 
        kpi_id: str, 
        client_id: str, 
        user_id: str, 
        profile_type: str, 
        timeframe: str, 
        currency: str
    ) -> Optional[Dict[str, Any]]:
        """Route to cache layer."""
        try:
            # Try personalized cache
            cached_result = self.cache_system.get_personalized(
                namespace="kpi:value",
                user_id=user_id,
                profile_type=profile_type,
                kpi_id=kpi_id,
                timeframe=timeframe,
                currency=currency
            )
            
            if cached_result:
                cached_result['source'] = 'cache'
                return cached_result
            
            return None
            
        except Exception as e:
            logger.error(f"❌ Cache layer error for KPI {kpi_id}: {e}")
            return None
    
    def _route_to_direct(
        self, 
        kpi_id: str, 
        client_id: str, 
        user_id: str, 
        profile_type: str, 
        timeframe: str, 
        currency: str
    ) -> Optional[Dict[str, Any]]:
        """Route to direct query layer."""
        try:
            # Use existing KpiService for direct calculation
            result = self.kpi_service.calculate_single_kpi(
                kpi_id=kpi_id,
                client_id=client_id,
                timeframe=timeframe,
                currency=currency
            )
            
            if result:
                result['source'] = 'direct'
                result['profile_type'] = profile_type
                return result
            
            return None
            
        except Exception as e:
            logger.error(f"❌ Direct layer error for KPI {kpi_id}: {e}")
            return None
    
    def _cache_result(
        self, 
        kpi_id: str, 
        client_id: str, 
        user_id: str, 
        profile_type: str, 
        timeframe: str, 
        currency: str, 
        result: Dict[str, Any]
    ):
        """Cache result for future requests."""
        try:
            # Remove routing metadata before caching
            cache_result = {k: v for k, v in result.items() if k != 'routing_metadata'}
            
            self.cache_system.set_personalized(
                namespace="kpi:value",
                user_id=user_id,
                value=cache_result,
                profile_type=profile_type,
                timeframe=timeframe,
                kpi_id=kpi_id,
                currency=currency
            )
            
        except Exception as e:
            logger.error(f"❌ Error caching result for KPI {kpi_id}: {e}")
    
    def get_routing_stats(self) -> Dict[str, Any]:
        """Get routing statistics and performance metrics."""
        try:
            cache_stats = self.cache_system.get_profile_stats()
            
            return {
                'cache_stats': cache_stats,
                'routing_strategies': self.routing_strategy,
                'available_layers': [layer.value for layer in QueryLayer],
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"❌ Error getting routing stats: {e}")
            return {'error': str(e)}
    
    def invalidate_user_data(self, user_id: str, kpi_id: Optional[str] = None):
        """Invalidate cached data for a user."""
        try:
            if kpi_id:
                # Invalidate specific KPI
                self.cache_system.invalidate_user_cache(user_id, f"kpi:value:kpi_id:{kpi_id}")
            else:
                # Invalidate all user data
                self.cache_system.invalidate_user_cache(user_id)

            logger.info(f"✅ Invalidated cache for user {user_id}" + (f" KPI {kpi_id}" if kpi_id else ""))

        except Exception as e:
            logger.error(f"❌ Error invalidating user data: {e}")

    def get_bcb_market_data(
        self,
        user_id: str,
        profile_type: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Get BCB market data with profile-aware caching.

        Args:
            user_id: User identifier
            profile_type: User profile for caching strategy

        Returns:
            BCB market data or error
        """
        start_time = time.time()

        try:
            logger.info(f"🏦 Fetching BCB market data for user {user_id}")

            # Check cache first (profile-aware TTL)
            cache_key = "bcb:market_data"
            cached_data = self.cache_system.get_personalized(
                namespace=cache_key,
                user_id=user_id,
                profile_type=profile_type
            )

            if cached_data:
                logger.info(f"✅ BCB data served from cache")
                return {
                    **cached_data,
                    'source': 'cache',
                    'cache_hit': True
                }

            # Fetch from BCB API (fail-fast)
            market_data = self.bcb_api.get_market_data_summary()

            # Cache with profile-aware TTL
            cache_ttl = self._get_bcb_cache_ttl(profile_type)
            self.cache_system.set_personalized(
                namespace=cache_key,
                user_id=user_id,
                value=market_data,
                profile_type=profile_type,
                ttl=cache_ttl
            )

            request_time = (time.time() - start_time) * 1000
            logger.info(f"✅ BCB market data fetched successfully ({request_time:.1f}ms)")

            return {
                **market_data,
                'source': 'bcb_api',
                'cache_hit': False,
                'request_time_ms': request_time
            }

        except Exception as e:
            logger.error(f"❌ BCB market data error: {e}")
            # Fail fast - no fallbacks
            return {
                'error': 'bcb_unavailable',
                'message': str(e),
                'source': 'bcb_api',
                'fail_fast': True,
                'request_time_ms': (time.time() - start_time) * 1000
            }

    def _get_bcb_cache_ttl(self, profile_type: Optional[str]) -> int:
        """Get BCB data cache TTL based on profile."""
        profile_ttl = {
            'CEO': 1800,      # 30 minutes - strategic view
            'CFO': 900,       # 15 minutes - financial planning
            'Risk_Manager': 300,  # 5 minutes - risk monitoring
            'Trader': 60,     # 1 minute - real-time trading
            'Operations': 600  # 10 minutes - operational view
        }
        return profile_ttl.get(profile_type, 600)  # Default 10 minutes


# Singleton instance
_smart_query_router: Optional[SmartQueryRouter] = None


def get_smart_query_router() -> SmartQueryRouter:
    """Get singleton instance of SmartQueryRouter."""
    global _smart_query_router
    if _smart_query_router is None:
        _smart_query_router = SmartQueryRouter()
    return _smart_query_router

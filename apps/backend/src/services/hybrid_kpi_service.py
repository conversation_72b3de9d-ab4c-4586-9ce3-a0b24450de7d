"""
Hybrid KPI Service for DataHero4 Week 3 Implementation
======================================================

Implements the 4 fundamental KPIs using the SmartQueryRouter and hybrid architecture:
1. spread_income_detailed - Detailed spread income by currency
2. margem_liquida_operacional - Operational net margin
3. custo_por_transacao - Cost per transaction
4. tempo_processamento_medio - Average processing time

Features:
- Uses SmartQueryRouter for intelligent 3-layer routing
- Profile-aware calculations and caching
- Real database queries (no mocks or fallbacks)
- Fail-fast validation and error handling

Author: DataHero4 Team
Date: 2025-01-21
"""

import logging
import time
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from sqlalchemy import text

from src.services.smart_query_router import get_smart_query_router
from src.config.kpi_definitions import get_kpi_definition, CURRENCY_CONFIG
from src.utils.learning_db_utils import get_db_manager

logger = logging.getLogger(__name__)


class HybridKpiService:
    """
    Service for calculating KPIs using hybrid architecture.
    
    Routes KPI calculations through SmartQueryRouter for optimal
    performance based on user profile and data requirements.
    """
    
    def __init__(self):
        """Initialize HybridKpiService."""
        self.router = get_smart_query_router()
        self.db_manager = get_db_manager()
        
        # KPI calculation methods mapping
        self.kpi_calculators = {
            'spread_income_detailed': self._calculate_spread_income_detailed,
            'margem_liquida_operacional': self._calculate_margem_liquida_operacional,
            'custo_por_transacao': self._calculate_custo_por_transacao,
            'tempo_processamento_medio': self._calculate_tempo_processamento_medio
        }
        
        logger.info("✅ HybridKpiService initialized with SmartQueryRouter")
    
    def calculate_kpi(
        self,
        kpi_id: str,
        client_id: str,
        user_id: str,
        timeframe: str = "week",
        currency: str = "all",
        profile_type: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Calculate KPI using hybrid architecture routing.
        
        Args:
            kpi_id: KPI identifier
            client_id: Client identifier
            user_id: User identifier
            timeframe: Data timeframe
            currency: Currency filter
            profile_type: User profile type
            
        Returns:
            KPI calculation result with routing metadata
        """
        start_time = time.time()
        
        logger.info(f"🧮 Calculating hybrid KPI: {kpi_id} for user {user_id}")
        
        try:
            # Validate KPI ID
            if kpi_id not in self.kpi_calculators:
                return {
                    'error': 'unsupported_kpi',
                    'message': f'KPI {kpi_id} not supported by HybridKpiService',
                    'supported_kpis': list(self.kpi_calculators.keys()),
                    'kpi_id': kpi_id
                }
            
            # Route through SmartQueryRouter first
            router_result = self.router.route_kpi_request(
                kpi_id=kpi_id,
                client_id=client_id,
                user_id=user_id,
                timeframe=timeframe,
                currency=currency,
                profile_type=profile_type
            )
            
            # If router found cached/snapshot data, return it
            if not router_result.get('error') and router_result.get('currentValue') is not None:
                logger.info(f"✅ KPI {kpi_id} served from {router_result.get('source', 'router')}")
                return router_result
            
            # If router failed or no data found, calculate directly
            logger.info(f"🔄 Router result incomplete, calculating {kpi_id} directly")
            
            # Calculate using specialized method
            calculator = self.kpi_calculators[kpi_id]
            calculation_result = calculator(
                client_id=client_id,
                user_id=user_id,
                timeframe=timeframe,
                currency=currency,
                profile_type=profile_type
            )
            
            if calculation_result and calculation_result.get('currentValue') is not None:
                # Add hybrid service metadata
                calculation_result['hybrid_metadata'] = {
                    'calculated_by': 'HybridKpiService',
                    'calculation_time_ms': (time.time() - start_time) * 1000,
                    'router_attempted': True,
                    'router_result': router_result.get('error', 'no_data'),
                    'calculated_at': datetime.now().isoformat()
                }
                
                # Cache result through router for future requests
                self.router.cache_system.set_personalized(
                    namespace="kpi:value",
                    user_id=user_id,
                    value=calculation_result,
                    profile_type=profile_type,
                    kpi_id=kpi_id,
                    timeframe=timeframe,
                    currency=currency
                )
                
                logger.info(f"✅ KPI {kpi_id} calculated and cached successfully")
                return calculation_result
            else:
                # Calculation failed
                logger.error(f"❌ KPI {kpi_id} calculation returned no data")
                return {
                    'error': 'calculation_failed',
                    'message': f'Failed to calculate KPI {kpi_id}',
                    'kpi_id': kpi_id,
                    'router_result': router_result,
                    'calculation_time_ms': (time.time() - start_time) * 1000
                }
                
        except Exception as e:
            logger.error(f"❌ Error calculating hybrid KPI {kpi_id}: {e}")
            return {
                'error': 'service_error',
                'message': str(e),
                'kpi_id': kpi_id,
                'calculation_time_ms': (time.time() - start_time) * 1000
            }
    
    def _get_timeframe_interval(self, timeframe: str) -> str:
        """Convert timeframe to SQL interval."""
        timeframe_map = {
            '1d': '1 day',
            'week': '7 days',
            'month': '30 days',
            'quarter': '90 days',
            'year': '365 days'
        }
        return timeframe_map.get(timeframe, '7 days')
    
    def _get_currency_filter(self, currency: str) -> str:
        """Get SQL currency filter."""
        if currency == "all":
            return "1=1"
        else:
            return f"currency = '{currency.upper()}'"
    
    def _calculate_spread_income_detailed(
        self,
        client_id: str,
        user_id: str,
        timeframe: str,
        currency: str,
        profile_type: Optional[str] = None
    ) -> Optional[Dict[str, Any]]:
        """Calculate detailed spread income KPI."""
        try:
            kpi_def = get_kpi_definition('spread_income_detailed')
            
            with self.db_manager.get_session() as session:
                # Build query with parameters
                query = text(kpi_def['sql_query'].format(
                    timeframe=self._get_timeframe_interval(timeframe),
                    currency_filter=self._get_currency_filter(currency)
                ))
                
                results = session.execute(query).fetchall()
                
                if not results:
                    logger.warning(f"No spread income data found for timeframe {timeframe}")
                    return None
                
                # Aggregate results
                total_spread_income = sum(row.spread_income for row in results)
                total_transactions = sum(row.transaction_count for row in results)
                avg_spread_percent = sum(row.avg_spread_percent for row in results) / len(results)
                
                # Format by currency
                currency_breakdown = {}
                for row in results:
                    if row.currency not in currency_breakdown:
                        currency_breakdown[row.currency] = {
                            'spread_income': 0,
                            'transaction_count': 0,
                            'dates': []
                        }
                    
                    currency_breakdown[row.currency]['spread_income'] += row.spread_income
                    currency_breakdown[row.currency]['transaction_count'] += row.transaction_count
                    currency_breakdown[row.currency]['dates'].append({
                        'date': row.date.isoformat(),
                        'spread_income': float(row.spread_income),
                        'transaction_count': row.transaction_count
                    })
                
                return {
                    'kpi_id': 'spread_income_detailed',
                    'currentValue': float(total_spread_income),
                    'formattedValue': f"${total_spread_income:,.2f}",
                    'title': kpi_def['name'],
                    'description': kpi_def['description'],
                    'unit': kpi_def['unit'],
                    'timeframe': timeframe,
                    'currency': currency,
                    'metadata': {
                        'total_transactions': total_transactions,
                        'avg_spread_percent': round(avg_spread_percent, 4),
                        'currency_breakdown': currency_breakdown,
                        'calculation_method': 'direct_sql'
                    },
                    'source': 'hybrid_calculation'
                }
                
        except Exception as e:
            logger.error(f"❌ Error calculating spread income detailed: {e}")
            return None
    
    def _calculate_margem_liquida_operacional(
        self,
        client_id: str,
        user_id: str,
        timeframe: str,
        currency: str,
        profile_type: Optional[str] = None
    ) -> Optional[Dict[str, Any]]:
        """Calculate operational net margin KPI."""
        try:
            kpi_def = get_kpi_definition('margem_liquida_operacional')
            
            with self.db_manager.get_session() as session:
                # Build query with parameters
                query = text(kpi_def['sql_query'].format(
                    timeframe=self._get_timeframe_interval(timeframe),
                    currency_filter=self._get_currency_filter(currency)
                ))
                
                results = session.execute(query).fetchall()
                
                if not results:
                    logger.warning(f"No operational margin data found for timeframe {timeframe}")
                    return None
                
                # Calculate weighted average margin
                total_revenue = sum(row.total_spread_revenue for row in results)
                total_costs = sum(row.total_costs for row in results)
                
                if total_revenue > 0:
                    operational_margin = ((total_revenue - total_costs) / total_revenue) * 100
                else:
                    operational_margin = 0.0
                
                # Daily breakdown
                daily_margins = []
                for row in results:
                    daily_margins.append({
                        'date': row.date.isoformat(),
                        'revenue': float(row.total_spread_revenue),
                        'costs': float(row.total_costs),
                        'margin_percent': float(row.operational_margin_percent)
                    })
                
                return {
                    'kpi_id': 'margem_liquida_operacional',
                    'currentValue': round(operational_margin, 2),
                    'formattedValue': f"{operational_margin:.2f}%",
                    'title': kpi_def['name'],
                    'description': kpi_def['description'],
                    'unit': kpi_def['unit'],
                    'timeframe': timeframe,
                    'currency': currency,
                    'metadata': {
                        'total_revenue': float(total_revenue),
                        'total_costs': float(total_costs),
                        'daily_margins': daily_margins,
                        'calculation_method': 'direct_sql'
                    },
                    'source': 'hybrid_calculation'
                }
                
        except Exception as e:
            logger.error(f"❌ Error calculating operational margin: {e}")
            return None

    def _calculate_custo_por_transacao(
        self,
        client_id: str,
        user_id: str,
        timeframe: str,
        currency: str,
        profile_type: Optional[str] = None
    ) -> Optional[Dict[str, Any]]:
        """Calculate cost per transaction KPI."""
        try:
            kpi_def = get_kpi_definition('custo_por_transacao')

            with self.db_manager.get_session() as session:
                # Build query with parameters
                query = text(kpi_def['sql_query'].format(
                    timeframe=self._get_timeframe_interval(timeframe),
                    currency_filter=self._get_currency_filter(currency)
                ))

                results = session.execute(query).fetchall()

                if not results:
                    logger.warning(f"No cost per transaction data found for timeframe {timeframe}")
                    return None

                # Calculate overall cost per transaction
                total_transactions = sum(row.transaction_count for row in results)
                total_costs = sum(row.total_costs for row in results)

                if total_transactions > 0:
                    avg_cost_per_transaction = total_costs / total_transactions
                else:
                    avg_cost_per_transaction = 0.0

                # Daily breakdown
                daily_costs = []
                for row in results:
                    daily_costs.append({
                        'date': row.date.isoformat(),
                        'transaction_count': row.transaction_count,
                        'total_costs': float(row.total_costs),
                        'cost_per_transaction': float(row.cost_per_transaction)
                    })

                return {
                    'kpi_id': 'custo_por_transacao',
                    'currentValue': round(avg_cost_per_transaction, 2),
                    'formattedValue': f"${avg_cost_per_transaction:.2f}",
                    'title': kpi_def['name'],
                    'description': kpi_def['description'],
                    'unit': kpi_def['unit'],
                    'timeframe': timeframe,
                    'currency': currency,
                    'metadata': {
                        'total_transactions': total_transactions,
                        'total_costs': float(total_costs),
                        'daily_costs': daily_costs,
                        'calculation_method': 'direct_sql'
                    },
                    'source': 'hybrid_calculation'
                }

        except Exception as e:
            logger.error(f"❌ Error calculating cost per transaction: {e}")
            return None

    def _calculate_tempo_processamento_medio(
        self,
        client_id: str,
        user_id: str,
        timeframe: str,
        currency: str,
        profile_type: Optional[str] = None
    ) -> Optional[Dict[str, Any]]:
        """Calculate average processing time KPI."""
        try:
            kpi_def = get_kpi_definition('tempo_processamento_medio')

            with self.db_manager.get_session() as session:
                # Build query with parameters
                query = text(kpi_def['sql_query'].format(
                    timeframe=self._get_timeframe_interval(timeframe),
                    currency_filter=self._get_currency_filter(currency)
                ))

                results = session.execute(query).fetchall()

                if not results:
                    logger.warning(f"No processing time data found for timeframe {timeframe}")
                    return None

                # Calculate overall average processing time
                total_transactions = sum(row.processed_transactions for row in results)
                weighted_avg_processing = sum(
                    row.avg_processing_seconds * row.processed_transactions
                    for row in results
                ) / total_transactions if total_transactions > 0 else 0.0

                # Daily breakdown
                daily_processing = []
                for row in results:
                    daily_processing.append({
                        'date': row.date.isoformat(),
                        'processed_transactions': row.processed_transactions,
                        'avg_processing_seconds': float(row.avg_processing_seconds),
                        'min_processing_seconds': float(row.min_processing_seconds),
                        'max_processing_seconds': float(row.max_processing_seconds),
                        'median_processing_seconds': float(row.median_processing_seconds),
                        'p95_processing_seconds': float(row.p95_processing_seconds)
                    })

                return {
                    'kpi_id': 'tempo_processamento_medio',
                    'currentValue': round(weighted_avg_processing, 2),
                    'formattedValue': f"{weighted_avg_processing:.2f}s",
                    'title': kpi_def['name'],
                    'description': kpi_def['description'],
                    'unit': kpi_def['unit'],
                    'timeframe': timeframe,
                    'currency': currency,
                    'metadata': {
                        'total_processed_transactions': total_transactions,
                        'daily_processing': daily_processing,
                        'calculation_method': 'direct_sql'
                    },
                    'source': 'hybrid_calculation'
                }

        except Exception as e:
            logger.error(f"❌ Error calculating processing time: {e}")
            return None

    def calculate_multiple_kpis(
        self,
        kpi_ids: List[str],
        client_id: str,
        user_id: str,
        timeframe: str = "week",
        currency: str = "all",
        profile_type: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Calculate multiple KPIs in batch.

        Args:
            kpi_ids: List of KPI identifiers
            client_id: Client identifier
            user_id: User identifier
            timeframe: Data timeframe
            currency: Currency filter
            profile_type: User profile type

        Returns:
            Dict with results for each KPI
        """
        logger.info(f"🧮 Calculating {len(kpi_ids)} hybrid KPIs for user {user_id}")

        results = {}
        start_time = time.time()

        for kpi_id in kpi_ids:
            try:
                result = self.calculate_kpi(
                    kpi_id=kpi_id,
                    client_id=client_id,
                    user_id=user_id,
                    timeframe=timeframe,
                    currency=currency,
                    profile_type=profile_type
                )
                results[kpi_id] = result

            except Exception as e:
                logger.error(f"❌ Error calculating KPI {kpi_id}: {e}")
                results[kpi_id] = {
                    'error': 'calculation_error',
                    'message': str(e),
                    'kpi_id': kpi_id
                }

        # Add batch metadata
        successful_kpis = len([r for r in results.values() if not r.get('error')])

        return {
            'kpis': results,
            'batch_metadata': {
                'total_kpis': len(kpi_ids),
                'successful_kpis': successful_kpis,
                'failed_kpis': len(kpi_ids) - successful_kpis,
                'batch_time_ms': (time.time() - start_time) * 1000,
                'calculated_at': datetime.now().isoformat()
            }
        }


# Singleton instance
_hybrid_kpi_service: Optional[HybridKpiService] = None


def get_hybrid_kpi_service() -> HybridKpiService:
    """Get singleton instance of HybridKpiService."""
    global _hybrid_kpi_service
    if _hybrid_kpi_service is None:
        _hybrid_kpi_service = HybridKpiService()
    return _hybrid_kpi_service

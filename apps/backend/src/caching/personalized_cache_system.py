"""
Personalized Cache System for DataHero4 Hybrid Architecture
===========================================================

Extends UnifiedCacheSystem with profile-aware caching strategies.
Implements TTL differentiation by user profile and personalized cache keys.

Features:
- Profile-specific TTL strategies (CEO=1h, Risk=5min, Trader=1min)
- User-aware cache keys (user_id + profile combination)
- Profile-based cache invalidation
- Hybrid architecture integration
- Fail-fast validation without fallbacks

Author: DataHero4 Team
Date: 2025-01-21
"""

import time
import logging
from typing import Any, Dict, Optional, List, Tuple
from datetime import datetime, timedelta
from sqlalchemy import text

from src.caching.unified_cache_system import UnifiedCacheSystem, CacheEntry
from src.utils.learning_db_utils import get_db_manager

logger = logging.getLogger(__name__)


class PersonalizedCacheSystem(UnifiedCacheSystem):
    """
    Profile-aware cache system extending UnifiedCacheSystem.
    
    Implements personalized caching strategies with profile-specific TTL,
    user-aware cache keys, and hybrid architecture integration.
    """
    
    def __init__(self, max_size: int = 2000, enable_stats: bool = True):
        """
        Initialize personalized cache system.
        
        Args:
            max_size: Maximum cache size (increased for profile data)
            enable_stats: Enable statistics tracking
        """
        super().__init__(max_size, enable_stats)
        
        # Profile-specific TTL configurations
        self.profile_ttl_config = {
            'CEO': {
                'kpi:value': 3600,      # 1 hour - strategic data
                'kpi:chart': 3600,      # 1 hour
                'query:result': 1800,   # 30 minutes
                'context:detection': 7200,  # 2 hours
                'default': 3600
            },
            'CFO': {
                'kpi:value': 1800,      # 30 minutes - financial data
                'kpi:chart': 1800,      # 30 minutes
                'query:result': 900,    # 15 minutes
                'context:detection': 3600,  # 1 hour
                'default': 1800
            },
            'Risk_Manager': {
                'kpi:value': 300,       # 5 minutes - risk data (real-time)
                'kpi:chart': 300,       # 5 minutes
                'query:result': 180,    # 3 minutes
                'context:detection': 600,   # 10 minutes
                'default': 300
            },
            'Trader': {
                'kpi:value': 60,        # 1 minute - operational data (real-time)
                'kpi:chart': 60,        # 1 minute
                'query:result': 30,     # 30 seconds
                'context:detection': 300,   # 5 minutes
                'default': 60
            },
            'Operations': {
                'kpi:value': 900,       # 15 minutes - operational efficiency
                'kpi:chart': 900,       # 15 minutes
                'query:result': 600,    # 10 minutes
                'context:detection': 1800,  # 30 minutes
                'default': 900
            }
        }
        
        # Profile detection cache
        self.profile_cache = {}
        self.profile_cache_ttl = 1800  # 30 minutes
        
        logger.info("✅ Personalized Cache System initialized with profile-aware TTL")
    
    def _generate_personalized_cache_key(
        self, 
        namespace: str, 
        user_id: str, 
        profile_type: Optional[str] = None,
        **kwargs
    ) -> str:
        """
        Generate personalized cache key including user_id and profile.
        
        Args:
            namespace: Cache namespace
            user_id: User identifier
            profile_type: User profile type (optional, will be detected if not provided)
            **kwargs: Additional parameters
            
        Returns:
            Personalized cache key
        """
        # Detect profile if not provided
        if profile_type is None:
            profile_type = self._get_user_profile(user_id)
        
        # Add user and profile to kwargs
        kwargs['user_id'] = user_id
        if profile_type:
            kwargs['profile'] = profile_type
        
        return self._generate_cache_key(namespace, **kwargs)
    
    def _get_profile_ttl(self, namespace: str, profile_type: str, timeframe: Optional[str] = None) -> int:
        """
        Get TTL based on profile type and namespace.
        
        Args:
            namespace: Cache namespace
            profile_type: User profile type
            timeframe: Optional timeframe for KPIs
            
        Returns:
            TTL in seconds
        """
        profile_config = self.profile_ttl_config.get(profile_type, self.profile_ttl_config['Operations'])
        
        # For KPIs with timeframe, use base TTL but adjust for profile
        if namespace.startswith('kpi:') and timeframe:
            base_ttl = profile_config.get('kpi:value', profile_config['default'])
            
            # Adjust TTL based on timeframe
            timeframe_multipliers = {
                '1d': 1.0,      # Base TTL
                'week': 1.5,    # 50% longer
                'month': 2.0,   # 2x longer
                'quarter': 3.0  # 3x longer
            }
            
            multiplier = timeframe_multipliers.get(timeframe, 1.0)
            return int(base_ttl * multiplier)
        
        # Use profile-specific TTL for namespace
        namespace_key = namespace.split(':')[0] + ':' + namespace.split(':')[1] if ':' in namespace else namespace
        return profile_config.get(namespace_key, profile_config['default'])
    
    def _get_user_profile(self, user_id: str) -> Optional[str]:
        """
        Get user profile from cache or database.
        
        Args:
            user_id: User identifier
            
        Returns:
            User profile type or None
        """
        # Check cache first
        cache_key = f"profile_detection:{user_id}"
        cached_profile = self.profile_cache.get(cache_key)
        
        if cached_profile:
            cache_time, profile_type = cached_profile
            if time.time() - cache_time < self.profile_cache_ttl:
                return profile_type
        
        # Query database
        try:
            db_manager = get_db_manager()
            with db_manager.get_session() as session:
                profile_sql = text("""
                    SELECT profile_type 
                    FROM user_profiles 
                    WHERE user_id = :user_id 
                    AND is_active = true
                    ORDER BY updated_at DESC
                    LIMIT 1;
                """)
                
                result = session.execute(profile_sql, {'user_id': user_id}).fetchone()
                
                if result:
                    profile_type = result.profile_type
                    # Cache the result
                    self.profile_cache[cache_key] = (time.time(), profile_type)
                    return profile_type
                
                # No profile found - fail fast
                logger.warning(f"⚠️ No profile found for user {user_id}")
                return None
                
        except Exception as e:
            logger.error(f"❌ Error getting user profile for {user_id}: {e}")
            # Fail fast - no fallback to default profile
            return None
    
    def get_personalized(
        self, 
        namespace: str, 
        user_id: str, 
        profile_type: Optional[str] = None,
        **kwargs
    ) -> Optional[Any]:
        """
        Get personalized cached value.
        
        Args:
            namespace: Cache namespace
            user_id: User identifier
            profile_type: User profile type (optional)
            **kwargs: Additional parameters
            
        Returns:
            Cached value or None
        """
        # Validate user_id
        if not user_id:
            logger.error("❌ user_id is required for personalized cache")
            return None
        
        # Generate personalized cache key
        cache_key = self._generate_personalized_cache_key(
            namespace=namespace,
            user_id=user_id,
            profile_type=profile_type,
            **kwargs
        )
        
        # Use parent get method with the personalized key
        return super().get(namespace, **{
            'user_id': user_id,
            'profile': profile_type or self._get_user_profile(user_id),
            **kwargs
        })
    
    def set_personalized(
        self,
        namespace: str,
        user_id: str,
        value: Any,
        profile_type: Optional[str] = None,
        ttl: Optional[int] = None,
        timeframe: Optional[str] = None,
        **kwargs
    ):
        """
        Set personalized cached value.
        
        Args:
            namespace: Cache namespace
            user_id: User identifier
            value: Value to cache
            profile_type: User profile type (optional)
            ttl: Custom TTL (optional)
            timeframe: Timeframe for KPIs (optional)
            **kwargs: Additional parameters
        """
        # Validate user_id
        if not user_id:
            logger.error("❌ user_id is required for personalized cache")
            return
        
        # Detect profile if not provided
        if profile_type is None:
            profile_type = self._get_user_profile(user_id)
        
        if not profile_type:
            logger.error(f"❌ Cannot cache without valid profile for user {user_id}")
            return
        
        # Determine TTL based on profile
        if ttl is None:
            ttl = self._get_profile_ttl(namespace, profile_type, timeframe)
        
        # Add profile metadata
        kwargs['user_id'] = user_id
        kwargs['profile'] = profile_type
        
        # Use parent set method
        super().set(
            namespace=namespace,
            value=value,
            ttl=ttl,
            timeframe=timeframe,
            **kwargs
        )
        
        logger.info(f"✅ Personalized cache SET for user {user_id} profile {profile_type} (TTL: {ttl}s)")
    
    def invalidate_user_cache(self, user_id: str, namespace: Optional[str] = None):
        """
        Invalidate all cache entries for a specific user.
        
        Args:
            user_id: User identifier
            namespace: Optional namespace filter
        """
        with self._lock:
            keys_to_remove = []
            
            for cache_key in self._cache:
                # Check if key contains user_id
                if f"user_id:{user_id}" in cache_key:
                    if namespace is None or cache_key.startswith(namespace):
                        keys_to_remove.append(cache_key)
            
            for key in keys_to_remove:
                del self._cache[key]
                if self.enable_stats:
                    self.stats["invalidations"] += 1
            
            # Also clear profile cache for this user
            profile_cache_key = f"profile_detection:{user_id}"
            if profile_cache_key in self.profile_cache:
                del self.profile_cache[profile_cache_key]
            
            if keys_to_remove:
                logger.info(f"🗑️ Invalidated {len(keys_to_remove)} personalized cache entries for user {user_id}")
    
    def invalidate_profile_cache(self, profile_type: str, namespace: Optional[str] = None):
        """
        Invalidate all cache entries for a specific profile type.
        
        Args:
            profile_type: Profile type to invalidate
            namespace: Optional namespace filter
        """
        with self._lock:
            keys_to_remove = []
            
            for cache_key in self._cache:
                # Check if key contains profile
                if f"profile:{profile_type}" in cache_key:
                    if namespace is None or cache_key.startswith(namespace):
                        keys_to_remove.append(cache_key)
            
            for key in keys_to_remove:
                del self._cache[key]
                if self.enable_stats:
                    self.stats["invalidations"] += 1
            
            if keys_to_remove:
                logger.info(f"🗑️ Invalidated {len(keys_to_remove)} cache entries for profile {profile_type}")
    
    def get_profile_stats(self) -> Dict[str, Any]:
        """Get statistics by profile type."""
        with self._lock:
            profile_stats = {}
            
            for cache_key, entry in self._cache.items():
                # Extract profile from metadata
                profile = entry.metadata.get('profile', 'unknown')
                
                if profile not in profile_stats:
                    profile_stats[profile] = {
                        'count': 0,
                        'total_age': 0,
                        'total_accesses': 0,
                        'avg_ttl': 0
                    }
                
                age = time.time() - entry.created_at
                profile_stats[profile]['count'] += 1
                profile_stats[profile]['total_age'] += age
                profile_stats[profile]['total_accesses'] += entry.access_count
                profile_stats[profile]['avg_ttl'] += entry.ttl
            
            # Calculate averages
            for profile, stats in profile_stats.items():
                if stats['count'] > 0:
                    stats['avg_age'] = round(stats['total_age'] / stats['count'], 2)
                    stats['avg_accesses'] = round(stats['total_accesses'] / stats['count'], 2)
                    stats['avg_ttl'] = round(stats['avg_ttl'] / stats['count'], 2)
            
            return {
                'general': self.get_stats(),
                'by_profile': profile_stats,
                'profile_cache_size': len(self.profile_cache)
            }
    
    def warm_up_profile_cache(self, user_ids: List[str]):
        """
        Warm up profile cache for multiple users.
        
        Args:
            user_ids: List of user IDs to warm up
        """
        logger.info(f"🔥 Warming up profile cache for {len(user_ids)} users")
        
        try:
            db_manager = get_db_manager()
            with db_manager.get_session() as session:
                # Batch query for all user profiles
                profiles_sql = text("""
                    SELECT user_id, profile_type 
                    FROM user_profiles 
                    WHERE user_id = ANY(:user_ids)
                    AND is_active = true;
                """)
                
                results = session.execute(profiles_sql, {'user_ids': user_ids}).fetchall()
                
                # Cache all results
                current_time = time.time()
                for result in results:
                    cache_key = f"profile_detection:{result.user_id}"
                    self.profile_cache[cache_key] = (current_time, result.profile_type)
                
                logger.info(f"✅ Warmed up profile cache for {len(results)} users")
                
        except Exception as e:
            logger.error(f"❌ Error warming up profile cache: {e}")


# Singleton instance
_personalized_cache: Optional[PersonalizedCacheSystem] = None


def get_personalized_cache() -> PersonalizedCacheSystem:
    """Get singleton instance of personalized cache system."""
    global _personalized_cache
    if _personalized_cache is None:
        _personalized_cache = PersonalizedCacheSystem()
    return _personalized_cache

"""
Unified FastAPI webapp for Railway deployment.
Direct implementation without mounting or copying routes.
"""

import os
import sys
import logging
import traceback
from contextlib import asynccontextmanager
from typing import Dict, Any
from datetime import datetime

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

# Import monitoring - Week 7
from src.monitoring.production_monitoring import setup_production_monitoring, get_monitoring_system
from src.monitoring.health_checks import get_health_checker
from src.monitoring.observability_system import initialize_observability, get_observability_system

# Configure logging
logging.basicConfig(
    level=os.getenv("LOG_LEVEL", "INFO"),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger(__name__)

logger.info("🚀 [UNIFIED] Starting unified webapp for Railway deployment - v3.0 FORCE REBUILD")

# Define lifespan for resource management
@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage application lifecycle."""
    logger.info("🚀 [UNIFIED] DataHero4 Unified starting up...")
    
    # Initialize critical components
    try:
        # Initialize cache warming
        from src.caching.context_singleton import ContextSingleton
        logger.info("🔥 [UNIFIED] Warming context cache...")
        ContextSingleton.preload_context("cambio", "L2M")
        
        # Initialize database connections
        from src.utils.learning_db_utils import get_db_manager
        db_manager = get_db_manager()
        if db_manager:
            logger.info("🔗 [UNIFIED] Database connections initialized")
        
        # Initialize production monitoring - Week 7
        logger.info("📊 [UNIFIED] Initializing production monitoring...")
        try:
            monitoring = setup_production_monitoring(app, "datahero4-unified")
            logger.info("✅ [UNIFIED] Production monitoring initialized")
        except Exception as monitor_error:
            logger.error(f"❌ [UNIFIED] Failed to initialize monitoring: {monitor_error}")
            # Don't fail startup for monitoring issues in development
            if os.getenv("ENVIRONMENT") == "production":
                raise

        logger.info("✅ [UNIFIED] Startup completed successfully")

    except Exception as e:
        logger.warning(f"⚠️ [UNIFIED] Startup warning: {e}")
        # Continue anyway for Railway deployment
    
    yield
    
    logger.info("🔄 [UNIFIED] DataHero4 Unified shutting down...")

# Create FastAPI app
app = FastAPI(
    title="DataHero4 Unified API",
    description="DataHero4 Railway deployment - unified routes",
    version="4.0.0-unified",
    lifespan=lifespan,
    docs_url="/docs" if os.getenv("ENVIRONMENT") != "production" else None,
    redoc_url="/redoc" if os.getenv("ENVIRONMENT") != "production" else None,
)

# CORS Configuration
origins_env = os.getenv("CORS_ALLOW_ORIGINS")
if origins_env:
    origins = [o.strip() for o in origins_env.split(",") if o.strip()]
else:
    origins = [
        "http://localhost:3000",
        "http://localhost:3001", 
        "https://datahero4.railway.app",
        "https://datahero4-frontend-production.up.railway.app",
        "https://*.railway.app",  # Allow all Railway subdomains
        "*"  # Allow all for Railway deployment (temporary)
    ]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Basic health check endpoint
@app.get("/health")
async def health_check() -> JSONResponse:
    """Health check endpoint for Railway."""
    try:
        return JSONResponse(
            status_code=200,
            content={
                "status": "healthy",
                "service": "datahero4-unified",
                "version": "4.0.0-unified",
                "environment": os.getenv("ENVIRONMENT", "production"),
                "timestamp": "2025-07-09T09:00:00Z"
            }
        )
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return JSONResponse(
            status_code=503,
            content={
                "status": "unhealthy",
                "service": "datahero4-unified",
                "error": str(e)
            }
        )

# Include routers directly
try:
    logger.info("📋 [UNIFIED] Including routers directly...")
    
    # Chat API router
    try:
        from src.interfaces.chat_api import chat_router
        app.include_router(chat_router)
        logger.info("✅ [UNIFIED] Chat router included")
    except Exception as e:
        logger.warning(f"⚠️ [UNIFIED] Chat router failed: {e}")
    
    # Dashboard API router
    try:
        from src.interfaces.dashboard_api import dashboard_router
        app.include_router(dashboard_router)
        logger.info("✅ [UNIFIED] Dashboard router included")
    except Exception as e:
        logger.warning(f"⚠️ [UNIFIED] Dashboard router failed: {e}")
    
    # Snapshot API router
    try:
        from src.api.dashboard_snapshot import router as snapshot_router
        app.include_router(snapshot_router)
        logger.info("✅ [UNIFIED] Snapshot router included")
    except Exception as e:
        logger.warning(f"⚠️ [UNIFIED] Snapshot router failed: {e}")
    
    # Health API router
    try:
        from src.api.health_routes import router as health_router
        app.include_router(health_router)
        logger.info("✅ [UNIFIED] Health router included")
    except Exception as e:
        logger.warning(f"⚠️ [UNIFIED] Health router failed: {e}")
    
    # Main API endpoints (ask, feedback, etc.)
    try:
        # Import critical models first
        from src.interfaces.api import AskRequest, AskResponse
        import uuid
        import time
        
        @app.post("/ask", response_model=AskResponse)
        async def ask(request: AskRequest) -> AskResponse:
            """Ask endpoint with basic implementation."""
            try:
                # Basic implementation for Railway deployment
                from src.graphs.optimized_workflow import create_optimized_workflow
                
                # Build basic state
                query_id = f"UNI_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{str(uuid.uuid4())[:8]}"
                
                # For Railway deployment, return a simple response
                return AskResponse(
                    query_id=query_id,
                    question=request.question,
                    sql_query="SELECT 1 as test_query",
                    results={"test": "Railway deployment active"},
                    direct_answer="Sistema DataHero4 ativo no Railway",
                    analysis_level="basic",
                    metadata={
                        "pipeline": "unified-railway",
                        "timestamp": datetime.now().isoformat(),
                        "status": "active"
                    }
                )
                
            except Exception as e:
                logger.error(f"Ask endpoint error: {e}")
                return AskResponse(
                    query_id=f"ERR_{str(uuid.uuid4())[:8]}",
                    question=request.question,
                    error=str(e),
                    direct_answer="Erro no processamento da consulta",
                    analysis_level="error"
                )
        
        logger.info("✅ [UNIFIED] Ask endpoint included")
        
    except Exception as e:
        logger.warning(f"⚠️ [UNIFIED] Ask endpoint failed: {e}")
    
    # Production monitoring endpoints - Week 7
    @app.get("/health")
    async def health_check():
        """Comprehensive health check endpoint - REAL CHECKS ONLY."""
        try:
            health_checker = get_health_checker()
            health_status = await health_checker.run_all_checks()

            # Return appropriate HTTP status based on health
            if health_status["status"] == "critical":
                return JSONResponse(
                    status_code=503,  # Service Unavailable
                    content=health_status
                )
            elif health_status["status"] == "warning":
                return JSONResponse(
                    status_code=200,  # OK but with warnings
                    content=health_status
                )
            else:
                return JSONResponse(
                    status_code=200,
                    content=health_status
                )

        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return JSONResponse(
                status_code=500,
                content={
                    "status": "unknown",
                    "error": str(e),
                    "timestamp": datetime.now().isoformat()
                }
            )

    @app.get("/health/quick")
    async def quick_health():
        """Quick health check for load balancers."""
        try:
            monitoring = get_monitoring_system()
            basic_health = monitoring.get_health_status()

            if basic_health["status"] == "healthy":
                return {"status": "ok"}
            else:
                return JSONResponse(
                    status_code=503,
                    content={"status": "unhealthy"}
                )
        except Exception as e:
            return JSONResponse(
                status_code=500,
                content={"status": "error", "message": str(e)}
            )

    @app.get("/ping")
    async def ping():
        """Simple ping endpoint."""
        return {"pong": True, "timestamp": datetime.now().isoformat()}
    
    @app.get("/debug-unified")
    async def debug_unified():
        """Debug endpoint to verify unified app is running."""
        return {
            "app": "webapp_unified",
            "version": "3.0",
            "status": "FORCE_REBUILD_ACTIVE",
            "total_routes": len(app.routes),
            "dashboard_routes": len([r for r in app.routes if hasattr(r, 'path') and '/api/dashboard' in r.path]),
            "all_routes": [r.path for r in app.routes if hasattr(r, 'path')],
            "timestamp": datetime.now().isoformat()
        }
    
    logger.info("✅ [UNIFIED] All routers included successfully")
    
except Exception as e:
    logger.error(f"❌ [UNIFIED] Failed to include routers: {e}")
    logger.error(f"❌ [UNIFIED] Traceback: {traceback.format_exc()}")

# Log final route count
@app.on_event("startup")
async def log_routes():
    """Log all available routes."""
    logger.info(f"🔍 [UNIFIED] Total routes available: {len(app.routes)}")
    for i, route in enumerate(app.routes):
        if hasattr(route, 'path') and not route.path.startswith('/docs'):
            logger.info(f"  {i+1}. {route.path}")

if __name__ == "__main__":
    import uvicorn
    port = int(os.getenv("PORT", 8000))
    logger.info(f"🚀 [UNIFIED] Starting server on port {port}")
    uvicorn.run(app, host="0.0.0.0", port=port)
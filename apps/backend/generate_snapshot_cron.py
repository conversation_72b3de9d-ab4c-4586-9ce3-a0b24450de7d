#!/usr/bin/env python3
"""
Script de geração de snapshot para Railway Cron Jobs.

Este script é projetado especificamente para execução via Railway Cron Schedule:
- Termina após execução (requisito Railway)
- Usa DATABASE_URL_LEARNING diretamente (postgres.railway.internal)
- Log estruturado para monitoramento
- Sem dependências de contexto FastAPI

Uso: python generate_snapshot_cron.py
"""

import json
import logging
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Optional

# Adicionar src ao path para imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.services.snapshot_service import SnapshotService
from src.services.profile_aware_snapshot_service import ProfileAwareSnapshotService
from src.utils.learning_db_utils import get_db_manager


def setup_logging() -> logging.Logger:
    """Configura logging estruturado para Railway."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )
    return logging.getLogger(__name__)


def log_structured(logger: logging.Logger, level: str, message: str, **kwargs):
    """Log estruturado compatível com Railway."""
    log_data = {
        "timestamp": datetime.utcnow().isoformat(),
        "level": level,
        "message": message,
        "service": "snapshot_cron",
        **kwargs
    }
    
    if level.upper() == "ERROR":
        logger.error(json.dumps(log_data))
    elif level.upper() == "WARNING":
        logger.warning(json.dumps(log_data))
    else:
        logger.info(json.dumps(log_data))


def generate_snapshot_task(logger: logging.Logger) -> bool:
    """
    Gera snapshot com KPIs calculados usando SnapshotService existente.
    
    Returns:
        bool: True se sucesso, False se falha
    """
    start_time = datetime.utcnow()
    
    try:
        log_structured(
            logger, "INFO", "Iniciando geração de snapshot",
            start_time=start_time.isoformat()
        )
        
        # Inicializar serviço de snapshot profile-aware
        snapshot_service = ProfileAwareSnapshotService()

        # Gerar snapshot tradicional (backward compatibility)
        log_structured(logger, "INFO", "Gerando snapshot tradicional...")
        traditional_snapshot = snapshot_service.generate_daily_snapshot("L2M")

        # Gerar snapshots por perfil (hybrid architecture)
        log_structured(logger, "INFO", "Gerando snapshots por perfil...")
        profile_results = snapshot_service.generate_profile_snapshots("L2M")

        # Verificar resultados combinados
        snapshot_result = traditional_snapshot
        
        if not snapshot_result:
            log_structured(
                logger, "ERROR", "Falha na geração de snapshot tradicional",
                error="generate_daily_snapshot returned None"
            )
            return False

        # Verificar resultados dos snapshots por perfil
        if profile_results.get('error'):
            log_structured(
                logger, "WARNING", "Falha parcial nos snapshots por perfil",
                error=profile_results['error'],
                successful_profiles=profile_results['summary'].get('successful_profiles', 0)
            )
        else:
            successful_profiles = profile_results['summary']['successful_profiles']
            total_profiles = profile_results['summary']['total_profiles']
            log_structured(
                logger, "INFO", "Snapshots por perfil gerados com sucesso",
                successful_profiles=successful_profiles,
                total_profiles=total_profiles,
                total_kpis=profile_results['summary']['total_kpis']
            )
        
        # Verificar se o snapshot foi gerado com sucesso
        metadata = snapshot_result.get("metadata", {})
        successful_kpis = metadata.get("successful_kpis", 0)
        total_kpis = metadata.get("kpi_count", 0)
        
        if successful_kpis == 0:
            log_structured(
                logger, "ERROR", "Nenhum KPI foi calculado com sucesso",
                successful_kpis=successful_kpis,
                total_kpis=total_kpis,
                failed_kpis=metadata.get("failed_kpis", [])
            )
            return False
        
        end_time = datetime.utcnow()
        duration = (end_time - start_time).total_seconds()
        
        log_structured(
            logger, "INFO", "Snapshot gerado com sucesso",
            successful_kpis=successful_kpis,
            total_kpis=total_kpis,
            duration_seconds=duration,
            snapshot_date=metadata.get("date"),
            storage_method="postgresql_with_fallback"
        )
        return True
            
    except Exception as e:
        log_structured(
            logger, "ERROR", "Erro inesperado na geração de snapshot",
            error=str(e),
            error_type=type(e).__name__
        )
        return False


def check_database_connectivity(logger: logging.Logger) -> bool:
    """Verifica conectividade com o banco de dados."""
    try:
        # Load local environment if exists
        from dotenv import load_dotenv
        from pathlib import Path
        
        env_file = Path(__file__).parent / ".env.local"
        if env_file.exists():
            load_dotenv(env_file)
            log_structured(logger, "INFO", "Loaded local environment variables", env_file=str(env_file))
        
        # Verificar variáveis de ambiente de banco
        db_url_learning = os.getenv('DATABASE_URL_LEARNING')
        db_url = os.getenv('DATABASE_URL')
        
        if not db_url_learning and not db_url:
            log_structured(
                logger, "ERROR", "Nenhuma URL de banco configurada",
                db_url_learning_present=bool(db_url_learning),
                db_url_present=bool(db_url)
            )
            return False
        
        # Verificar se é a URL interna do Railway
        active_db_url = db_url_learning or db_url
        is_railway_internal = active_db_url and "postgres.railway.internal" in active_db_url
        is_railway_env = os.getenv('RAILWAY_ENVIRONMENT') is not None
        
        log_structured(
            logger, "INFO", "Verificando configuração do banco",
            is_railway_internal=is_railway_internal,
            is_railway_env=is_railway_env,
            db_host_type="railway_internal" if is_railway_internal else "external",
            active_db="learning" if db_url_learning else "main"
        )
        
        return True
        
    except Exception as e:
        log_structured(
            logger, "ERROR", "Erro ao verificar conectividade do banco",
            error=str(e)
        )
        return False


def main():
    """Função principal do script de cron."""
    logger = setup_logging()
    
    log_structured(
        logger, "INFO", "=== INICIANDO RAILWAY SNAPSHOT CRON ===",
        version="1.0.0",
        environment=os.getenv("RAILWAY_ENVIRONMENT", "unknown")
    )
    
    try:
        # Verificar conectividade do banco
        if not check_database_connectivity(logger):
            log_structured(
                logger, "ERROR", "Falha na conectividade do banco - abortando"
            )
            sys.exit(1)
        
        # Gerar snapshot
        success = generate_snapshot_task(logger)
        
        if success:
            log_structured(
                logger, "INFO", "=== SNAPSHOT CRON CONCLUÍDO COM SUCESSO ==="
            )
            sys.exit(0)
        else:
            log_structured(
                logger, "ERROR", "=== SNAPSHOT CRON FALHOU ==="
            )
            sys.exit(1)
            
    except KeyboardInterrupt:
        log_structured(
            logger, "WARNING", "Script interrompido pelo usuário"
        )
        sys.exit(130)
    except Exception as e:
        log_structured(
            logger, "ERROR", "Erro fatal no script de cron",
            error=str(e),
            error_type=type(e).__name__
        )
        sys.exit(1)


if __name__ == "__main__":
    # Garantir que o script termine após execução (requisito Railway)
    main() 
"""
Test Configuration for Week 6 Testing & Validation - NO MOCKS
=============================================================

Real test configuration for Week 6 testing suite.
Tests REAL components with REAL database connections.
NO MOCKS, NO FALLBACKS - fail fast and fail loud.

Author: DataHero4 Team
Date: 2025-01-21
"""

import os
import sys
import pytest
from unittest.mock import Mock, patch
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Load environment variables from .env file
from dotenv import load_dotenv
env_path = project_root / '.env'
if env_path.exists():
    load_dotenv(env_path)
    print(f"✅ Loaded environment variables from {env_path}")
else:
    print(f"⚠️  .env file not found at {env_path}")

# Set test-specific environment variables
os.environ.setdefault('DATABASE_URL', 'postgresql://test:test@localhost:5432/test_db')
os.environ.setdefault('DB_LEARNING_PASSWORD', 'test_password')
os.environ.setdefault('DB_LEARNING_HOST', 'localhost')
os.environ.setdefault('DB_LEARNING_PORT', '5432')
os.environ.setdefault('DB_LEARNING_USER', 'test')
os.environ.setdefault('DB_LEARNING_NAME', 'test_learning_db')
os.environ.setdefault('REDIS_URL', 'redis://localhost:6379/1')  # Test Redis DB


@pytest.fixture(scope="session", autouse=True)
def setup_test_environment():
    """Setup test environment with real configuration."""
    print("🧪 Setting up Week 6 test environment - REAL COMPONENTS ONLY...")

    # Verify required environment variables exist
    required_vars = [
        'DATABASE_URL',
        'DB_LEARNING_PASSWORD',
        'DB_LEARNING_HOST',
        'DB_LEARNING_PORT',
        'DB_LEARNING_USER',
        'DB_LEARNING_NAME'
    ]

    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)

    if missing_vars:
        pytest.fail(f"❌ Missing required environment variables: {missing_vars}")

    print("✅ Test environment configured with real database connections")
    yield
    print("🧹 Test environment cleanup completed")


@pytest.fixture
def mock_database_connection():
    """Mock database connections for unit tests."""
    with patch('sqlalchemy.create_engine') as mock_engine:
        mock_connection = Mock()
        mock_engine.return_value = mock_connection
        yield mock_connection


@pytest.fixture(scope="class")
def mock_kpi_service():
    """Mock KpiService for testing."""
    mock_instance = Mock()

    # Mock common methods
    mock_instance.get_kpi_value.return_value = {
        'value': 1000.0,
        'timestamp': '2025-01-21T10:00:00',
        'source': 'test'
    }

    return mock_instance


@pytest.fixture(scope="class")
def mock_learning_db_manager():
    """Mock LearningDBManager for testing."""
    mock_instance = Mock()

    # Mock session context manager
    mock_session = Mock()
    mock_instance.get_session.return_value.__enter__.return_value = mock_session
    mock_instance.get_session.return_value.__exit__.return_value = None

    return mock_instance


@pytest.fixture(scope="class")
def mock_redis_cache():
    """Mock Redis cache for testing."""
    mock_instance = Mock()

    # Mock Redis operations
    mock_instance.get.return_value = None
    mock_instance.set.return_value = True
    mock_instance.delete.return_value = 1
    mock_instance.ping.return_value = True

    return mock_instance


@pytest.fixture
def sample_kpi_data():
    """Sample KPI data for testing."""
    return {
        'spread_income_detailed': {
            'value': 1500.75,
            'timestamp': '2025-01-21T10:00:00',
            'source': 'test',
            'metadata': {
                'profile_type': 'CEO',
                'timeframe': 'week',
                'currency': 'BRL'
            }
        },
        'margem_liquida_operacional': {
            'value': 25.5,
            'timestamp': '2025-01-21T10:00:00',
            'source': 'test',
            'metadata': {
                'profile_type': 'CFO',
                'timeframe': 'month',
                'currency': 'BRL'
            }
        }
    }


@pytest.fixture
def sample_profiles():
    """Sample user profiles for testing."""
    return {
        'CEO': {
            'strategy': 'snapshot',
            'ttl': 3600,
            'priority': 'accuracy',
            'characteristics': ['strategic', 'high_level', 'long_term']
        },
        'CFO': {
            'strategy': 'snapshot',
            'ttl': 1800,
            'priority': 'accuracy',
            'characteristics': ['financial', 'detailed', 'analytical']
        },
        'Risk_Manager': {
            'strategy': 'direct',
            'ttl': 300,
            'priority': 'real_time',
            'characteristics': ['monitoring', 'alerts', 'immediate']
        },
        'Trader': {
            'strategy': 'cache',
            'ttl': 60,
            'priority': 'speed',
            'characteristics': ['fast', 'frequent', 'real_time']
        },
        'Operations': {
            'strategy': 'cache',
            'ttl': 900,
            'priority': 'efficiency',
            'characteristics': ['operational', 'routine', 'efficient']
        }
    }


@pytest.fixture
def performance_test_config():
    """Configuration for performance tests."""
    return {
        'cache_hit_rate_targets': {
            'CEO': 0.60,
            'CFO': 0.60,
            'Risk_Manager': 0.30,
            'Trader': 0.80,
            'Operations': 0.80
        },
        'response_time_targets': {
            'snapshot': 500,  # ms
            'cache': 100,     # ms
            'direct': 1000    # ms
        },
        'concurrent_user_targets': {
            'success_rate': 0.95,
            'max_users': 50,
            'requests_per_user': 5
        },
        'memory_limits': {
            'max_increase_mb': 100,
            'baseline_samples': 10,
            'load_requests': 1000
        }
    }


@pytest.fixture(autouse=True)
def isolate_tests():
    """Ensure test isolation by clearing caches and resetting state."""
    # Clear any global state before each test
    yield
    # Cleanup after each test
    pass


# Pytest configuration
def pytest_configure(config):
    """Configure pytest with custom markers."""
    config.addinivalue_line(
        "markers", "unit: Unit tests for individual components"
    )
    config.addinivalue_line(
        "markers", "integration: Integration tests for multi-layer architecture"
    )
    config.addinivalue_line(
        "markers", "performance: Performance tests with specific metrics validation"
    )
    config.addinivalue_line(
        "markers", "slow: Slow running tests (>5 seconds)"
    )
    config.addinivalue_line(
        "markers", "database: Tests that require database connection"
    )
    config.addinivalue_line(
        "markers", "external: Tests that require external services"
    )


def pytest_collection_modifyitems(config, items):
    """Modify test collection to add markers automatically."""
    for item in items:
        # Add markers based on test file location
        if "unit" in str(item.fspath):
            item.add_marker(pytest.mark.unit)
        elif "integration" in str(item.fspath):
            item.add_marker(pytest.mark.integration)
        elif "performance" in str(item.fspath):
            item.add_marker(pytest.mark.performance)
        
        # Add slow marker for tests that might be slow
        if "sustained_load" in item.name or "concurrent" in item.name:
            item.add_marker(pytest.mark.slow)


# Test result reporting
def pytest_terminal_summary(terminalreporter, exitstatus, config):
    """Custom terminal summary for Week 6 tests."""
    if hasattr(terminalreporter, 'stats'):
        passed = len(terminalreporter.stats.get('passed', []))
        failed = len(terminalreporter.stats.get('failed', []))
        errors = len(terminalreporter.stats.get('error', []))
        
        print(f"\n🧪 WEEK 6 TEST SUMMARY:")
        print(f"✅ Passed: {passed}")
        print(f"❌ Failed: {failed}")
        print(f"🚨 Errors: {errors}")
        
        if exitstatus == 0:
            print("🎉 All Week 6 tests passed!")
        else:
            print("⚠️  Some Week 6 tests failed - check logs above")

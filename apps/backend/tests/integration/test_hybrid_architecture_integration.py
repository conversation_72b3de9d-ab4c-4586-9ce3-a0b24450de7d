"""
Integration Tests for Hybrid Architecture - DataHero4 Week 6
===========================================================

Comprehensive integration tests for the 3-layer hybrid architecture.
Tests end-to-end routing through snapshot → cache → direct layers with profile-aware optimization.

Features tested:
- Multi-layer routing integration (snapshot, cache, direct)
- Profile-aware routing strategies end-to-end
- Cache hit rate validation by profile (>80% target)
- Fail-fast behavior across all layers
- Performance benchmarks for each layer
- Concurrent user scenarios
- Real database integration

Author: DataHero4 Team
Date: 2025-01-21
"""

import pytest
import asyncio
import time
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed

# Import with mocking to avoid database dependencies
with patch('src.services.kpi_service.KpiService'), \
     patch('src.utils.learning_db_utils.LearningDBManager'), \
     patch('redis.Redis'):
    from src.services.smart_query_router import get_smart_query_router
    from src.services.hybrid_kpi_service import get_hybrid_kpi_service
    from src.services.profile_detector import get_profile_detector
    from src.caching.personalized_cache_system import get_personalized_cache


class TestHybridArchitectureIntegration:
    """Integration test suite for hybrid architecture."""

    @pytest.fixture(scope="class")
    def router(self, mock_kpi_service, mock_learning_db_manager, mock_redis_cache):
        """Get SmartQueryRouter instance with mocked dependencies."""
        with patch('src.services.kpi_service.get_kpi_service', return_value=mock_kpi_service), \
             patch('src.utils.learning_db_utils.get_db_manager', return_value=mock_learning_db_manager), \
             patch('redis.Redis', return_value=mock_redis_cache):
            return get_smart_query_router()

    @pytest.fixture(scope="class")
    def hybrid_service(self, mock_kpi_service, mock_learning_db_manager, mock_redis_cache):
        """Get HybridKpiService instance with mocked dependencies."""
        with patch('src.services.kpi_service.get_kpi_service', return_value=mock_kpi_service), \
             patch('src.utils.learning_db_utils.get_db_manager', return_value=mock_learning_db_manager), \
             patch('redis.Redis', return_value=mock_redis_cache):
            return get_hybrid_kpi_service()

    @pytest.fixture(scope="class")
    def profile_detector(self, mock_learning_db_manager):
        """Get ProfileDetector instance with mocked dependencies."""
        with patch('src.utils.learning_db_utils.get_db_manager', return_value=mock_learning_db_manager):
            return get_profile_detector()

    @pytest.fixture(scope="class")
    def cache_system(self, mock_redis_cache):
        """Get PersonalizedCacheSystem instance with mocked dependencies."""
        with patch('redis.Redis', return_value=mock_redis_cache):
            return get_personalized_cache()

    @pytest.fixture
    def test_profiles(self):
        """Test profiles with expected routing strategies."""
        return {
            "CEO": {"strategy": "snapshot", "ttl": 3600, "priority": "accuracy"},
            "CFO": {"strategy": "snapshot", "ttl": 1800, "priority": "accuracy"},
            "Risk_Manager": {"strategy": "direct", "ttl": 300, "priority": "real_time"},
            "Trader": {"strategy": "cache", "ttl": 60, "priority": "speed"},
            "Operations": {"strategy": "cache", "ttl": 900, "priority": "efficiency"}
        }

    @pytest.fixture
    def test_kpis(self):
        """Test KPIs for integration testing."""
        return [
            "spread_income_detailed",
            "margem_liquida_operacional",
            "custo_por_transacao",
            "tempo_processamento_medio"
        ]

    def test_end_to_end_routing_by_profile(self, router, test_profiles, test_kpis):
        """Test end-to-end routing for each profile type."""
        results = {}
        
        for profile_type, config in test_profiles.items():
            profile_results = []
            
            for kpi_id in test_kpis:
                start_time = time.time()
                
                result = router.route_kpi_request(
                    kpi_id=kpi_id,
                    client_id="L2M",
                    user_id=f"test_user_{profile_type.lower()}",
                    timeframe="week",
                    currency="BRL",
                    profile_type=profile_type
                )
                
                end_time = time.time()
                request_time = (end_time - start_time) * 1000  # ms
                
                # Validate result structure
                assert result is not None
                assert 'routing_metadata' in result
                assert result['routing_metadata']['strategy'] == config['strategy']
                
                profile_results.append({
                    'kpi_id': kpi_id,
                    'strategy': result['routing_metadata']['strategy'],
                    'request_time_ms': request_time,
                    'cache_hit': result.get('cache_hit', False),
                    'source': result.get('source', 'unknown')
                })
            
            results[profile_type] = profile_results

        # Validate routing strategies
        for profile_type, profile_results in results.items():
            expected_strategy = test_profiles[profile_type]['strategy']
            for result in profile_results:
                assert result['strategy'] == expected_strategy, \
                    f"Profile {profile_type} should use {expected_strategy} strategy"

    def test_cache_hit_rate_by_profile(self, router, cache_system, test_profiles):
        """Test cache hit rates meet >80% target for cache-optimized profiles."""
        cache_profiles = ["Trader", "Operations"]  # Cache-optimized profiles
        kpi_id = "spread_income_detailed"
        
        for profile_type in cache_profiles:
            user_id = f"cache_test_user_{profile_type.lower()}"
            
            # Clear any existing cache
            cache_system.invalidate_user_cache(user_id)
            
            # First request (cache miss expected)
            result1 = router.route_kpi_request(
                kpi_id=kpi_id,
                client_id="L2M",
                user_id=user_id,
                timeframe="week",
                currency="BRL",
                profile_type=profile_type
            )
            
            # Subsequent requests (cache hits expected)
            hits = 0
            total_requests = 10
            
            for i in range(total_requests):
                result = router.route_kpi_request(
                    kpi_id=kpi_id,
                    client_id="L2M",
                    user_id=user_id,
                    timeframe="week",
                    currency="BRL",
                    profile_type=profile_type
                )
                
                if result.get('cache_hit', False):
                    hits += 1
            
            hit_rate = hits / total_requests
            assert hit_rate >= 0.80, \
                f"Profile {profile_type} cache hit rate {hit_rate:.2f} below 80% target"

    def test_performance_benchmarks_by_layer(self, router, test_profiles):
        """Test performance benchmarks for each routing layer."""
        kpi_id = "spread_income_detailed"
        performance_targets = {
            "snapshot": 500,  # ms
            "cache": 100,     # ms
            "direct": 1000    # ms
        }
        
        for profile_type, config in test_profiles.items():
            strategy = config['strategy']
            user_id = f"perf_test_user_{profile_type.lower()}"
            
            # Warm up cache for cache strategy
            if strategy == "cache":
                router.route_kpi_request(
                    kpi_id=kpi_id,
                    client_id="L2M",
                    user_id=user_id,
                    timeframe="week",
                    currency="BRL",
                    profile_type=profile_type
                )
            
            # Measure performance
            times = []
            for i in range(5):  # 5 samples
                start_time = time.time()
                
                result = router.route_kpi_request(
                    kpi_id=kpi_id,
                    client_id="L2M",
                    user_id=user_id,
                    timeframe="week",
                    currency="BRL",
                    profile_type=profile_type
                )
                
                end_time = time.time()
                request_time = (end_time - start_time) * 1000  # ms
                times.append(request_time)
            
            avg_time = sum(times) / len(times)
            target_time = performance_targets[strategy]
            
            assert avg_time <= target_time, \
                f"Profile {profile_type} ({strategy}) avg time {avg_time:.1f}ms exceeds {target_time}ms target"

    def test_concurrent_users_different_profiles(self, router, test_profiles):
        """Test concurrent users with different profiles."""
        kpi_id = "spread_income_detailed"
        concurrent_users = 20
        
        def make_request(profile_type, user_index):
            user_id = f"concurrent_user_{profile_type.lower()}_{user_index}"
            
            start_time = time.time()
            result = router.route_kpi_request(
                kpi_id=kpi_id,
                client_id="L2M",
                user_id=user_id,
                timeframe="week",
                currency="BRL",
                profile_type=profile_type
            )
            end_time = time.time()
            
            return {
                'profile_type': profile_type,
                'user_id': user_id,
                'success': result is not None,
                'request_time_ms': (end_time - start_time) * 1000,
                'strategy': result.get('routing_metadata', {}).get('strategy', 'unknown') if result else None
            }
        
        # Create concurrent requests
        with ThreadPoolExecutor(max_workers=concurrent_users) as executor:
            futures = []
            
            for i in range(concurrent_users):
                profile_type = list(test_profiles.keys())[i % len(test_profiles)]
                future = executor.submit(make_request, profile_type, i)
                futures.append(future)
            
            # Collect results
            results = []
            for future in as_completed(futures):
                result = future.result()
                results.append(result)
        
        # Validate results
        assert len(results) == concurrent_users
        
        successful_requests = [r for r in results if r['success']]
        success_rate = len(successful_requests) / len(results)
        
        assert success_rate >= 0.95, f"Success rate {success_rate:.2f} below 95% target"
        
        # Validate strategy distribution
        strategy_counts = {}
        for result in successful_requests:
            strategy = result['strategy']
            strategy_counts[strategy] = strategy_counts.get(strategy, 0) + 1
        
        assert len(strategy_counts) >= 2, "Should use multiple routing strategies"

    def test_fail_fast_behavior_across_layers(self, router):
        """Test fail-fast behavior across all layers."""
        # Test invalid parameters
        with pytest.raises(ValueError):
            router.route_kpi_request(
                kpi_id="",  # Empty KPI ID
                client_id="L2M",
                user_id="test_user",
                profile_type="CEO"
            )
        
        with pytest.raises(ValueError):
            router.route_kpi_request(
                kpi_id="test_kpi",
                client_id="",  # Empty client ID
                user_id="test_user",
                profile_type="CEO"
            )
        
        with pytest.raises(ValueError):
            router.route_kpi_request(
                kpi_id="test_kpi",
                client_id="L2M",
                user_id="",  # Empty user ID
                profile_type="CEO"
            )

    def test_profile_detection_integration(self, profile_detector, router):
        """Test integration between profile detection and routing."""
        user_id = "integration_test_user"
        
        # Mock query history for CEO pattern
        with patch.object(profile_detector, 'detect_profile') as mock_detect:
            mock_detect.return_value = {
                'detected_profile': 'CEO',
                'confidence': 0.85,
                'reason': 'strategic_queries',
                'analysis': {}
            }
            
            # Detect profile
            detection_result = profile_detector.detect_profile(user_id)
            detected_profile = detection_result['detected_profile']
            
            # Use detected profile for routing
            result = router.route_kpi_request(
                kpi_id="spread_income_detailed",
                client_id="L2M",
                user_id=user_id,
                profile_type=detected_profile
            )
            
            assert result is not None
            assert result['routing_metadata']['strategy'] == 'snapshot'  # CEO uses snapshot

    def test_bcb_integration_with_profiles(self, router, test_profiles):
        """Test BCB API integration with different profiles."""
        for profile_type in test_profiles.keys():
            user_id = f"bcb_test_user_{profile_type.lower()}"
            
            # Test BCB market data request
            result = router.get_bcb_market_data(
                user_id=user_id,
                profile_type=profile_type
            )
            
            # Should either succeed or fail fast (no fallbacks)
            assert result is not None
            
            if result.get('error'):
                # Fail-fast behavior
                assert result.get('fail_fast') is True
                assert 'message' in result
            else:
                # Successful response
                assert 'usd_brl_quotation' in result or 'cache_hit' in result

    def test_hybrid_service_integration(self, hybrid_service, test_profiles, test_kpis):
        """Test HybridKpiService integration with routing."""
        for profile_type in ["CEO", "Trader"]:  # Test different strategies
            user_id = f"hybrid_test_user_{profile_type.lower()}"
            
            for kpi_id in test_kpis[:2]:  # Test subset for performance
                result = hybrid_service.get_kpi_value(
                    kpi_id=kpi_id,
                    client_id="L2M",
                    user_id=user_id,
                    timeframe="week",
                    currency="BRL",
                    profile_type=profile_type
                )
                
                assert result is not None
                if not result.get('error'):
                    assert 'value' in result or 'currentValue' in result
                    assert 'source' in result

    def test_cache_invalidation_across_layers(self, router, cache_system):
        """Test cache invalidation affects all layers appropriately."""
        user_id = "cache_invalidation_test_user"
        profile_type = "Trader"  # Cache-optimized profile
        kpi_id = "spread_income_detailed"
        
        # Make initial request (populate cache)
        result1 = router.route_kpi_request(
            kpi_id=kpi_id,
            client_id="L2M",
            user_id=user_id,
            profile_type=profile_type
        )
        
        # Make second request (should hit cache)
        result2 = router.route_kpi_request(
            kpi_id=kpi_id,
            client_id="L2M",
            user_id=user_id,
            profile_type=profile_type
        )
        
        # Invalidate cache
        router.invalidate_user_data(user_id, kpi_id)
        
        # Make third request (should miss cache)
        result3 = router.route_kpi_request(
            kpi_id=kpi_id,
            client_id="L2M",
            user_id=user_id,
            profile_type=profile_type
        )
        
        # Validate cache behavior
        assert result1 is not None
        assert result2 is not None
        assert result3 is not None
        
        # Second request should be faster (cache hit)
        # Third request should be slower (cache miss after invalidation)

    def test_routing_statistics_collection(self, router, test_profiles):
        """Test routing statistics collection across all layers."""
        # Generate some routing activity
        for profile_type in test_profiles.keys():
            user_id = f"stats_test_user_{profile_type.lower()}"
            
            router.route_kpi_request(
                kpi_id="spread_income_detailed",
                client_id="L2M",
                user_id=user_id,
                profile_type=profile_type
            )
        
        # Get routing statistics
        stats = router.get_routing_stats()
        
        assert stats is not None
        assert 'routing_strategies' in stats
        assert 'cache_strategies' in stats
        assert 'profile_mappings' in stats
        
        # Validate strategy usage
        strategies = stats['routing_strategies']
        assert 'snapshot' in strategies
        assert 'cache' in strategies
        assert 'direct' in strategies

    def test_error_propagation_across_layers(self, router):
        """Test error propagation and handling across layers."""
        user_id = "error_test_user"
        
        # Test with invalid KPI ID
        result = router.route_kpi_request(
            kpi_id="nonexistent_kpi_id",
            client_id="L2M",
            user_id=user_id,
            profile_type="CEO"
        )
        
        # Should handle error gracefully
        assert result is not None
        if result.get('error'):
            assert 'message' in result or 'error' in result

    @pytest.mark.slow
    def test_sustained_load_performance(self, router, test_profiles):
        """Test sustained load performance across all layers."""
        duration_seconds = 30
        requests_per_second = 10
        total_requests = duration_seconds * requests_per_second
        
        start_time = time.time()
        successful_requests = 0
        
        for i in range(total_requests):
            profile_type = list(test_profiles.keys())[i % len(test_profiles)]
            user_id = f"load_test_user_{i}"
            
            try:
                result = router.route_kpi_request(
                    kpi_id="spread_income_detailed",
                    client_id="L2M",
                    user_id=user_id,
                    profile_type=profile_type
                )
                
                if result and not result.get('error'):
                    successful_requests += 1
                    
            except Exception as e:
                pass  # Count as failed request
            
            # Maintain request rate
            expected_time = start_time + (i + 1) / requests_per_second
            current_time = time.time()
            if current_time < expected_time:
                time.sleep(expected_time - current_time)
        
        end_time = time.time()
        actual_duration = end_time - start_time
        success_rate = successful_requests / total_requests
        
        assert success_rate >= 0.90, f"Success rate {success_rate:.2f} below 90% under sustained load"
        assert actual_duration <= duration_seconds * 1.1, "Test took too long (rate limiting failed)"

[tool.poetry]
name = "datahero4-cli"
version = "0.1.0"
description = "CLI Interativa para DataHero4 - Versão Simplificada"
authors = ["DataHero Team"]
packages = [{include = "src"}]

[tool.poetry.dependencies]
python = "^3.9,<3.13"
# Core CLI dependencies
typer = "^0.9.0"
rich = "^13.0.0"
python-dotenv = "^1.0.0"
pydantic = "^2.0.0"
fastapi = "^0.100.0"
uvicorn = "^0.20.0"
sqlalchemy = "^2.0.0"
requests = "^2.28.0"
PyYAML = "^6.0.0"
pandas = "^2.0.0"
psutil = "^5.9.0"
psycopg2-binary = "^2.9.9"
# LLM Providers (compatible versions)
together = "^0.2.10"
anthropic = "^0.25.0"
openai = "^1.0.0"
# LangGraph for workflow orchestration
langgraph = "^0.2.0"
langchain = "^0.3.0"
langchain-core = "^0.3.0"
pydantic-settings = "^2.0.0"
# SQL parsing and validation
sqlglot = "^25.0.0"
# Additional utilities
pyyaml = "^6.0.0"
# Redis for caching
redis = {extras = ["hiredis"], version = "^5.0.0"}
# APScheduler for internal scheduling
apscheduler = "^3.10.0"
tiktoken = "^0.9.0"
sqlmodel = "^0.0.24"
prometheus-fastapi-instrumentator = "6.1.0"
structlog = "^25.4.0"
opentelemetry-api = "^1.35.0"
opentelemetry-sdk = "^1.35.0"
opentelemetry-instrumentation-fastapi = "^0.56b0"
opentelemetry-exporter-prometheus = "^0.56b0"

[tool.poetry.group.dev.dependencies]
pytest = "^7.0.0"
pytest-asyncio = "^0.23.0"
pytest-cov = "^4.0.0"
ruff = "^0.1.0"
httpx = "^0.28.1"
pytest-postgresql = "^7.0.2"

[tool.poetry.scripts]
datahero4 = "src.cli.enhanced_chat_cli:main"
datahero4-simple = "src.cli.interactive_chat_cli:main"
datahero4-feedback = "src.cli.feedback_cli:main"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

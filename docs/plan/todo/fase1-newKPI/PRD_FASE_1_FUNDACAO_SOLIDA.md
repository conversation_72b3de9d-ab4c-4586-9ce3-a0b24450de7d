# PRD - Fase 1: Fundação Sólida - DataHero4

## 📋 Informações do Documento

| Campo | Valor |
|-------|-------|
| **Versão** | 1.0 |
| **Data** | Janeiro 2025 |
| **Autor** | Equipe DataHero4 |
| **Status** | Em Desenvolvimento |
| **Duração** | 8 semanas |
| **Complexidade** | BAIXA-MÉDIA |

## 🎯 Visão Geral

### **Objetivo da Fase 1**
Implementar **personalização de KPIs por perfil de usuário** e **novos KPIs fundamentais** aproveitando 100% da arquitetura LangGraph existente, sem quebrar funcionalidades atuais.

### **Princípios de Implementação**
- ✅ **Backward Compatibility**: Não quebrar funcionalidades existentes
- ✅ **Incremental Development**: Adicionar features sem modificar core
- ✅ **Feature Flags**: Controle granular de rollout
- ✅ **Zero Downtime**: Deploy sem interrupção de serviço

## 🏗️ Arquitetura Atual (Base Existente)

### **Componentes Existentes que Serão Aproveitados**
```yaml
backend_existente:
  langgraph_core:
    - DataHeroState: "Estado principal do workflow"
    - enhanced_coordinator_agent: "Coordenador principal"
    - main_graph.py: "Workflow LangGraph"
    - optimized_workflow.py: "Workflow otimizado"
  
  api_layer:
    - api.py: "FastAPI principal"
    - dashboard_api.py: "Endpoints dashboard"
    - chat_api.py: "Endpoints conversacionais"
  
  data_layer:
    - kpi_models.py: "Modelos KPI existentes"
    - learning_db_utils.py: "Utilitários banco"
    - unified_cache_system.py: "Sistema cache"
  
  security:
    - security.py: "Middleware segurança"
    - ApiKeyValidator: "Validação API keys"

frontend_existente:
  components:
    - KpiBentoCard.tsx: "Cards KPI individuais"
    - KpiBentoGrid.tsx: "Grid layout KPIs"
    - KPIDrawer.tsx: "Drawer detalhado"
  
  hooks:
    - useKpis.tsx: "Hook dados KPIs"
    - useDashboardFilters.tsx: "Hook filtros"
    - useKPIDrawer.tsx: "Hook drawer"
```

### **Pontos de Extensão Identificados**
```python
# Pontos onde podemos adicionar funcionalidades sem quebrar
extension_points = {
    "DataHeroState": "Adicionar campos user_profile e kpi_preferences",
    "dashboard_api.py": "Novos endpoints /profile e /personalized-kpis",
    "kpi_models.py": "Novos modelos para KPIs personalizados",
    "unified_cache_system.py": "Cache por usuário",
    "KpiBentoGrid.tsx": "Filtros por perfil",
    "useKpis.tsx": "Lógica personalização"
}
```

## 📊 Novos KPIs - Nível 1 (Fundamentais)

### **KPI 1: Spread Income Detalhado**
```yaml
spread_income_detalhado:
  id: "spread_income_detailed"
  nome: "Spread Income Detalhado"
  formula: "SUM(spread_percent * volume) por moeda"
  criticidade: "CRÍTICO"
  perfis_alvo: ["CEO", "CFO", "Trader"]
  frequencia_calculo: "Tempo real"
  
  implementacao:
    tabela_origem: "transactions (existente)"
    campos_necessarios: ["spread", "amount", "currency"]
    agregacao: "GROUP BY currency, DATE(created_at)"
    cache_ttl: "300 segundos"
```

### **KPI 2: Margem Líquida Operacional**
```yaml
margem_liquida_operacional:
  id: "operational_net_margin"
  nome: "Margem Líquida Operacional"
  formula: "(Receita Spread - Custos Operacionais) / Receita Total * 100"
  criticidade: "CRÍTICO"
  perfis_alvo: ["CEO", "CFO"]
  frequencia_calculo: "Diário"
  
  implementacao:
    dados_necessarios: 
      - receita_spread: "Calculado do spread_income_detailed"
      - custos_operacionais: "Nova tabela operational_costs"
    nova_tabela: "operational_costs"
    cache_ttl: "3600 segundos"
```

### **KPI 3: Custo por Transação**
```yaml
custo_por_transacao:
  id: "cost_per_transaction"
  nome: "Custo por Transação"
  formula: "Custos Operacionais Totais / Número de Transações"
  criticidade: "ALTO"
  perfis_alvo: ["CFO", "Operações"]
  frequencia_calculo: "Diário"
  
  implementacao:
    calculo: "operational_costs.total / COUNT(transactions)"
    agregacao: "Por dia, semana, mês"
    cache_ttl: "1800 segundos"
```

### **KPI 4: Tempo Processamento Médio**
```yaml
tempo_processamento_medio:
  id: "avg_processing_time"
  nome: "Tempo Processamento Médio"
  formula: "AVG(processed_at - created_at) em segundos"
  criticidade: "CRÍTICO"
  perfis_alvo: ["Operações", "Risk"]
  frequencia_calculo: "Tempo real"
  
  implementacao:
    campos_adicionais: "processed_at timestamp na tabela transactions"
    calculo: "EXTRACT(EPOCH FROM (processed_at - created_at))"
    cache_ttl: "60 segundos"
```

## 👥 Sistema de Perfis de Usuário

### **Perfis Implementados na Fase 1**
```yaml
perfis_fase_1:
  ceo:
    nome: "CEO/Diretoria"
    kpis_prioritarios: 
      - spread_income_detailed
      - operational_net_margin
      - cost_per_transaction
    dashboard_layout: "executive_summary"
    refresh_frequency: "daily"
  
  cfo:
    nome: "CFO/Diretor Financeiro"
    kpis_prioritarios:
      - operational_net_margin
      - cost_per_transaction
      - spread_income_detailed
    dashboard_layout: "financial_focus"
    refresh_frequency: "daily"
  
  operations:
    nome: "Gerente de Operações"
    kpis_prioritarios:
      - avg_processing_time
      - cost_per_transaction
      - operational_efficiency
    dashboard_layout: "operational_monitoring"
    refresh_frequency: "real_time"
```

### **Detecção Automática de Perfil**
```python
# Estratégia de detecção baseada no codebase existente
profile_detection_strategy = {
    "user_role_field": "Usar campo user_role existente no DataHeroState",
    "fallback_detection": "Baseado em padrões de uso de KPIs",
    "manual_override": "Interface para seleção manual",
    "default_profile": "operations (mais genérico)"
}
```

## 🔧 Arquitetura Técnica Detalhada

### **Extensões do Backend**

#### **1. Extensão do DataHeroState**
```python
# apps/backend/src/graphs/state.py (EXTENSÃO)
class DataHeroState(TypedDict):
    # Campos existentes mantidos...
    question: str
    client_id: str
    sector: str
    user_role: Optional[str]  # JÁ EXISTE
    
    # NOVOS CAMPOS (compatíveis)
    user_profile: Optional[Dict[str, Any]]  # Perfil detalhado
    kpi_preferences: Optional[Dict[str, Any]]  # Preferências KPI
    personalized_kpis: Optional[List[str]]  # KPIs selecionados
    profile_detected: Optional[bool]  # Se perfil foi detectado
```

#### **2. Novos Modelos de Dados**
```python
# apps/backend/src/models/profile_models.py (NOVO)
from sqlmodel import SQLModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime

class UserProfile(SQLModel, table=True):
    """Perfil do usuário para personalização."""
    __tablename__ = "user_profiles"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    user_id: str = Field(index=True, unique=True)
    profile_type: str = Field(index=True)  # ceo, cfo, operations, etc.
    preferences: Dict[str, Any] = Field(default_factory=dict)
    selected_kpis: List[str] = Field(default_factory=list)
    dashboard_layout: str = Field(default="default")
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: Optional[datetime] = None

class KpiDefinition(SQLModel, table=True):
    """Definições de KPIs personalizáveis."""
    __tablename__ = "kpi_definitions"
    
    id: str = Field(primary_key=True)
    name: str
    description: str
    formula: str
    category: str
    criticality: str  # CRITICAL, HIGH, MEDIUM, LOW
    target_profiles: List[str] = Field(default_factory=list)
    calculation_frequency: str = Field(default="daily")
    cache_ttl: int = Field(default=3600)
    is_active: bool = Field(default=True)
```

#### **3. Novos Endpoints API**
```python
# apps/backend/src/interfaces/profile_api.py (NOVO)
from fastapi import APIRouter, Depends, HTTPException
from src.models.profile_models import UserProfile, KpiDefinition

profile_router = APIRouter(prefix="/api/profile", tags=["profile"])

@profile_router.get("/detect/{user_id}")
async def detect_user_profile(user_id: str):
    """Detecta perfil do usuário automaticamente."""
    pass

@profile_router.post("/configure")
async def configure_user_profile(profile_data: UserProfile):
    """Configura perfil do usuário."""
    pass

@profile_router.get("/kpis/{profile_type}")
async def get_profile_kpis(profile_type: str):
    """Retorna KPIs recomendados para o perfil."""
    pass
```

#### **4. Extensão do Dashboard API**
```python
# apps/backend/src/interfaces/dashboard_api.py (EXTENSÃO)
# Adicionar novos endpoints sem modificar existentes

@dashboard_router.get("/personalized-kpis/{user_id}")
async def get_personalized_kpis(user_id: str):
    """Retorna KPIs personalizados para o usuário."""
    pass

@dashboard_router.get("/kpi-recommendations/{user_id}")
async def get_kpi_recommendations(user_id: str):
    """Recomenda KPIs baseado no perfil."""
    pass
```

### **Extensões do Frontend**

#### **1. Novo Hook de Personalização**
```typescript
// apps/frontend/src/hooks/usePersonalization.ts (NOVO)
import { useState, useEffect } from 'react';
import { UserProfile, KpiDefinition } from '@/types/profile';

export const usePersonalization = (userId: string) => {
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [recommendedKpis, setRecommendedKpis] = useState<KpiDefinition[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Detectar perfil automaticamente
  const detectProfile = async () => {
    // Implementação
  };

  // Configurar perfil
  const configureProfile = async (profileData: UserProfile) => {
    // Implementação
  };

  return {
    profile,
    recommendedKpis,
    isLoading,
    detectProfile,
    configureProfile
  };
};
```

#### **2. Componente de Configuração de Perfil**
```typescript
// apps/frontend/src/components/profile/ProfileSetup.tsx (NOVO)
import React from 'react';
import { usePersonalization } from '@/hooks/usePersonalization';

export const ProfileSetup: React.FC = () => {
  // Componente para configuração inicial do perfil
  // Integra com sistema existente sem quebrar
};
```

#### **3. Extensão do KpiBentoGrid**
```typescript
// apps/frontend/src/components/dashboard/KpiBentoGrid.tsx (EXTENSÃO)
// Adicionar lógica de filtro por perfil sem modificar estrutura existente

const KpiBentoGrid: React.FC<KpiBentoGridProps> = ({ 
  kpis, 
  filters, 
  togglePriority,
  userProfile // NOVO PROP
}) => {
  // Filtrar KPIs baseado no perfil
  const filteredKpis = useMemo(() => {
    if (!userProfile) return kpis;
    return kpis.filter(kpi => 
      userProfile.selectedKpis.includes(kpi.id) ||
      kpi.targetProfiles.includes(userProfile.profileType)
    );
  }, [kpis, userProfile]);

  // Resto da implementação mantida igual
};
```

## 🗄️ Esquema de Banco de Dados

### **Novas Tabelas (Adicionais)**
```sql
-- Tabela de perfis de usuário
CREATE TABLE user_profiles (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(255) UNIQUE NOT NULL,
    profile_type VARCHAR(50) NOT NULL,
    preferences JSONB DEFAULT '{}',
    selected_kpis TEXT[] DEFAULT '{}',
    dashboard_layout VARCHAR(50) DEFAULT 'default',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP
);

-- Tabela de definições de KPIs
CREATE TABLE kpi_definitions (
    id VARCHAR(100) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    formula TEXT NOT NULL,
    category VARCHAR(50) NOT NULL,
    criticality VARCHAR(20) NOT NULL,
    target_profiles TEXT[] DEFAULT '{}',
    calculation_frequency VARCHAR(20) DEFAULT 'daily',
    cache_ttl INTEGER DEFAULT 3600,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Tabela de custos operacionais (para novos KPIs)
CREATE TABLE operational_costs (
    id SERIAL PRIMARY KEY,
    date DATE NOT NULL,
    category VARCHAR(100) NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Adicionar campo processed_at à tabela transactions existente
ALTER TABLE transactions 
ADD COLUMN processed_at TIMESTAMP;

-- Índices para performance
CREATE INDEX idx_user_profiles_user_id ON user_profiles(user_id);
CREATE INDEX idx_user_profiles_profile_type ON user_profiles(profile_type);
CREATE INDEX idx_kpi_definitions_category ON kpi_definitions(category);
CREATE INDEX idx_operational_costs_date ON operational_costs(date);
CREATE INDEX idx_transactions_processed_at ON transactions(processed_at);
```

## 🔄 Integração com Sistema Existente

### **Pontos de Integração Sem Quebra**

#### **1. LangGraph Workflow**
```python
# Extensão do enhanced_coordinator_agent
def enhanced_coordinator_agent(state: DataHeroState) -> Dict[str, Any]:
    # Lógica existente mantida
    existing_logic = original_coordinator_logic(state)
    
    # NOVA LÓGICA: Personalização baseada em perfil
    if state.get("user_profile"):
        personalized_result = apply_profile_personalization(
            existing_logic, 
            state["user_profile"]
        )
        return personalized_result
    
    return existing_logic
```

#### **2. Sistema de Cache**
```python
# Extensão do unified_cache_system.py
class UnifiedCacheSystem:
    def get_personalized(self, namespace: str, user_id: str, **kwargs):
        """Cache personalizado por usuário."""
        personalized_key = f"{namespace}:user:{user_id}"
        return self.get(personalized_key, **kwargs)
    
    def set_personalized(self, namespace: str, user_id: str, value: Any, **kwargs):
        """Cache personalizado por usuário."""
        personalized_key = f"{namespace}:user:{user_id}"
        return self.set(personalized_key, value, **kwargs)
```

#### **3. API Middleware**
```python
# Extensão do security.py
class ProfileMiddleware:
    """Middleware para injeção de perfil do usuário."""
    
    async def __call__(self, request: Request, call_next):
        # Detectar usuário (API key, JWT, etc.)
        user_id = self.extract_user_id(request)
        
        if user_id:
            # Carregar perfil do usuário
            profile = await self.load_user_profile(user_id)
            request.state.user_profile = profile
        
        return await call_next(request)
```

## 📈 Critérios de Sucesso

### **Métricas Técnicas**
```yaml
metricas_tecnicas:
  performance:
    - tempo_resposta_kpis_personalizados: "< 500ms"
    - tempo_deteccao_perfil: "< 200ms"
    - cache_hit_rate_personalizado: "> 80%"
    - uptime_novos_endpoints: "> 99.9%"
  
  qualidade:
    - cobertura_testes_novos_componentes: "> 90%"
    - bugs_criticos_producao: "0"
    - precisao_deteccao_perfil: "> 85%"
    - compatibilidade_backward: "100%"
```

### **Métricas de Negócio**
```yaml
metricas_negocio:
  adocao:
    - usuarios_configuraram_perfil: "> 70%"
    - tempo_configuracao_inicial: "< 3 minutos"
    - taxa_retencao_personalizacao: "> 90%"
  
  satisfacao:
    - nps_novos_kpis: "> 8.0"
    - reducao_tempo_encontrar_info: "> 40%"
    - aumento_engajamento_dashboard: "> 30%"
```

## 🧪 Estratégia de Testes

### **Testes de Compatibilidade**
```yaml
testes_compatibilidade:
  regression_tests:
    - todos_endpoints_existentes_funcionais
    - langgraph_workflow_inalterado
    - cache_system_compativel
    - frontend_components_funcionais
  
  integration_tests:
    - novos_endpoints_integrados
    - perfil_detection_funcional
    - kpis_personalizados_calculados
    - cache_personalizado_operacional
```

### **Testes de Performance**
```yaml
testes_performance:
  load_testing:
    - 1000_usuarios_simultaneos_dashboard
    - 500_deteccoes_perfil_simultaneas
    - cache_personalizado_sob_carga
  
  stress_testing:
    - picos_calculo_kpis_personalizados
    - memoria_usage_novos_componentes
    - database_performance_novas_tabelas
```

## 🚀 Plano de Execução Detalhado

### **Semana 1-2: Fundação e Novos KPIs**

#### **Tarefas Backend (Semana 1)**
```yaml
backend_semana_1:
  database_setup:
    - criar_tabelas_user_profiles_kpi_definitions
    - adicionar_campo_processed_at_transactions
    - criar_tabela_operational_costs
    - executar_migrations_producao

  models_extension:
    - criar_profile_models.py
    - estender_kpi_models.py_novos_kpis
    - criar_kpi_calculator_enhanced.py
    - testes_unitarios_models
```

#### **Tarefas Backend (Semana 2)**
```yaml
backend_semana_2:
  kpi_implementation:
    - implementar_spread_income_detalhado
    - implementar_margem_liquida_operacional
    - implementar_custo_por_transacao
    - implementar_tempo_processamento_medio

  api_endpoints:
    - criar_profile_api.py
    - estender_dashboard_api.py_endpoints_personalizados
    - middleware_profile_injection
    - testes_integracao_apis
```

#### **Tarefas Frontend (Semana 1-2)**
```yaml
frontend_semana_1_2:
  hooks_services:
    - criar_usePersonalization.ts
    - estender_useKpis.ts_personalizacao
    - criar_profileService.ts
    - tipos_typescript_profiles

  components:
    - criar_ProfileSetup.tsx
    - estender_KpiBentoGrid.tsx_filtros_perfil
    - criar_KpiSelector.tsx
    - testes_componentes_jest
```

### **Semana 3-4: Sistema de Perfis e Detecção**

#### **Implementação Detecção Automática**
```python
# apps/backend/src/services/profile_detector.py
class ProfileDetector:
    """Detecta perfil baseado no codebase existente."""

    def __init__(self, db_session: Session):
        self.db = db_session
        self.kpi_usage_analyzer = KpiUsageAnalyzer()

    async def detect_profile_from_usage(self, user_id: str) -> Optional[str]:
        """Detecta perfil baseado em padrões de uso."""

        # Analisar histórico de KPIs acessados
        usage_history = await self.get_user_kpi_usage(user_id)

        if not usage_history:
            return None

        # Padrões de detecção
        patterns = {
            "ceo": ["margem_liquida", "volume_total", "crescimento"],
            "cfo": ["custo", "margem", "rentabilidade", "fluxo_caixa"],
            "operations": ["tempo_processamento", "uptime", "eficiencia"],
            "risk": ["exposicao", "limite", "var", "risco"]
        }

        # Calcular score por perfil
        profile_scores = {}
        for profile, keywords in patterns.items():
            score = sum(1 for kpi in usage_history if any(kw in kpi.lower() for kw in keywords))
            profile_scores[profile] = score / len(usage_history)

        # Retornar perfil com maior score (se > 0.3)
        best_profile = max(profile_scores.items(), key=lambda x: x[1])
        return best_profile[0] if best_profile[1] > 0.3 else None

    async def detect_profile_from_role(self, user_role: str) -> Optional[str]:
        """Detecta perfil baseado no campo user_role existente."""

        role_mapping = {
            "ceo": "ceo",
            "diretor": "ceo",
            "presidente": "ceo",
            "cfo": "cfo",
            "financeiro": "cfo",
            "operacoes": "operations",
            "operacional": "operations",
            "risco": "risk",
            "risk": "risk"
        }

        if not user_role:
            return None

        user_role_lower = user_role.lower()
        for key, profile in role_mapping.items():
            if key in user_role_lower:
                return profile

        return None
```

#### **Sistema de Recomendação de KPIs**
```python
# apps/backend/src/services/kpi_recommender.py
class KpiRecommender:
    """Recomenda KPIs baseado no perfil e comportamento."""

    def __init__(self, db_session: Session):
        self.db = db_session

    async def get_recommended_kpis(self, profile_type: str) -> List[KpiDefinition]:
        """Retorna KPIs recomendados para o perfil."""

        # KPIs base por perfil
        base_recommendations = {
            "ceo": [
                "spread_income_detailed",
                "operational_net_margin",
                "cost_per_transaction",
                "volume_total_negociado"
            ],
            "cfo": [
                "operational_net_margin",
                "cost_per_transaction",
                "spread_income_detailed",
                "cash_flow_operacional"
            ],
            "operations": [
                "avg_processing_time",
                "cost_per_transaction",
                "uptime_sistema",
                "throughput_transacoes"
            ]
        }

        recommended_ids = base_recommendations.get(profile_type, [])

        # Buscar definições no banco
        stmt = select(KpiDefinition).where(
            KpiDefinition.id.in_(recommended_ids),
            KpiDefinition.is_active == True
        )

        result = await self.db.execute(stmt)
        return result.scalars().all()

    async def get_peer_popular_kpis(self, profile_type: str) -> List[str]:
        """Retorna KPIs populares entre usuários do mesmo perfil."""

        # Query para encontrar KPIs mais usados por perfil
        stmt = text("""
            SELECT unnest(selected_kpis) as kpi_id, COUNT(*) as usage_count
            FROM user_profiles
            WHERE profile_type = :profile_type
            GROUP BY kpi_id
            ORDER BY usage_count DESC
            LIMIT 10
        """)

        result = await self.db.execute(stmt, {"profile_type": profile_type})
        return [row.kpi_id for row in result]
```

### **Semana 5-6: Integração BCB e Cache Personalizado**

#### **Integração Banco Central Brasil**
```python
# apps/backend/src/services/bcb_integration.py
import aiohttp
import asyncio
from typing import Dict, Any, Optional
from datetime import datetime, timedelta

class BCBIntegration:
    """Integração com API do Banco Central do Brasil."""

    BASE_URL = "https://api.bcb.gov.br/dados/serie"

    def __init__(self, cache_system):
        self.cache = cache_system
        self.session = None

    async def get_session(self):
        """Lazy loading da sessão HTTP."""
        if self.session is None:
            self.session = aiohttp.ClientSession()
        return self.session

    async def get_official_usd_rate(self) -> Dict[str, Any]:
        """Cotação oficial USD/BRL."""

        # Verificar cache primeiro
        cached = self.cache.get("bcb_usd_rate")
        if cached:
            return cached

        try:
            session = await self.get_session()
            url = f"{self.BASE_URL}/bcdata.sgs.1/dados/ultimos/1"

            async with session.get(url, params={"formato": "json"}) as response:
                if response.status == 200:
                    data = await response.json()

                    if data:
                        result = {
                            "rate": float(data[0]["valor"]),
                            "date": data[0]["data"],
                            "source": "BCB",
                            "last_update": datetime.now().isoformat()
                        }

                        # Cache por 30 minutos
                        self.cache.set("bcb_usd_rate", result, ttl=1800)
                        return result

            return {"error": "No data available"}

        except Exception as e:
            logger.error(f"Erro ao buscar cotação BCB: {e}")
            return {"error": str(e)}

    async def get_selic_rate(self) -> Dict[str, Any]:
        """Taxa SELIC atual."""

        cached = self.cache.get("bcb_selic_rate")
        if cached:
            return cached

        try:
            session = await self.get_session()
            url = f"{self.BASE_URL}/bcdata.sgs.432/dados/ultimos/1"

            async with session.get(url, params={"formato": "json"}) as response:
                if response.status == 200:
                    data = await response.json()

                    if data:
                        result = {
                            "rate": float(data[0]["valor"]),
                            "date": data[0]["data"],
                            "source": "BCB",
                            "last_update": datetime.now().isoformat()
                        }

                        # Cache por 1 hora (SELIC muda raramente)
                        self.cache.set("bcb_selic_rate", result, ttl=3600)
                        return result

            return {"error": "No data available"}

        except Exception as e:
            logger.error(f"Erro ao buscar SELIC BCB: {e}")
            return {"error": str(e)}
```

#### **Cache Personalizado Estendido**
```python
# apps/backend/src/caching/personalized_cache.py
from src.caching.unified_cache_system import UnifiedCacheSystem

class PersonalizedCacheSystem(UnifiedCacheSystem):
    """Extensão do cache para personalização por usuário."""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.user_cache_prefix = "user"

    def get_user_kpis(self, user_id: str, timeframe: str = "30d") -> Optional[Any]:
        """Cache específico para KPIs do usuário."""
        return self.get(
            namespace=f"{self.user_cache_prefix}:kpis",
            user_id=user_id,
            timeframe=timeframe
        )

    def set_user_kpis(self, user_id: str, kpis_data: Any, timeframe: str = "30d"):
        """Cache específico para KPIs do usuário."""
        # TTL baseado no timeframe
        ttl_map = {
            "1d": 300,    # 5 minutos
            "7d": 900,    # 15 minutos
            "30d": 1800,  # 30 minutos
            "90d": 3600   # 1 hora
        }

        ttl = ttl_map.get(timeframe, 1800)

        self.set(
            namespace=f"{self.user_cache_prefix}:kpis",
            value=kpis_data,
            ttl=ttl,
            user_id=user_id,
            timeframe=timeframe
        )

    def get_user_profile(self, user_id: str) -> Optional[Any]:
        """Cache do perfil do usuário."""
        return self.get(
            namespace=f"{self.user_cache_prefix}:profile",
            user_id=user_id
        )

    def set_user_profile(self, user_id: str, profile_data: Any):
        """Cache do perfil do usuário."""
        # Perfil cache por 24 horas
        self.set(
            namespace=f"{self.user_cache_prefix}:profile",
            value=profile_data,
            ttl=86400,
            user_id=user_id
        )

    def invalidate_user_cache(self, user_id: str):
        """Invalida todo cache do usuário."""
        patterns = [
            f"{self.user_cache_prefix}:kpis:user:{user_id}:*",
            f"{self.user_cache_prefix}:profile:user:{user_id}:*"
        ]

        for pattern in patterns:
            self.invalidate_pattern(pattern)
```

### **Semana 7-8: Testes e Refinamentos**

#### **Estratégia de Testes Abrangente**
```python
# tests/integration/test_personalization_flow.py
import pytest
from fastapi.testclient import TestClient
from src.interfaces.api import app

class TestPersonalizationFlow:
    """Testes de integração do fluxo de personalização."""

    def setup_method(self):
        self.client = TestClient(app)
        self.test_user_id = "test_user_123"

    def test_profile_detection_flow(self):
        """Testa fluxo completo de detecção de perfil."""

        # 1. Detectar perfil automaticamente
        response = self.client.get(f"/api/profile/detect/{self.test_user_id}")
        assert response.status_code == 200

        profile_data = response.json()
        assert "detected_profile" in profile_data
        assert "confidence" in profile_data

    def test_personalized_kpis_flow(self):
        """Testa fluxo de KPIs personalizados."""

        # 1. Configurar perfil
        profile_config = {
            "user_id": self.test_user_id,
            "profile_type": "ceo",
            "selected_kpis": ["spread_income_detailed", "operational_net_margin"]
        }

        response = self.client.post("/api/profile/configure", json=profile_config)
        assert response.status_code == 200

        # 2. Buscar KPIs personalizados
        response = self.client.get(f"/api/dashboard/personalized-kpis/{self.test_user_id}")
        assert response.status_code == 200

        kpis_data = response.json()
        assert len(kpis_data["kpis"]) >= 2
        assert any(kpi["id"] == "spread_income_detailed" for kpi in kpis_data["kpis"])

    def test_cache_personalization(self):
        """Testa cache personalizado."""

        # 1. Primeira requisição (cache miss)
        response1 = self.client.get(f"/api/dashboard/personalized-kpis/{self.test_user_id}")
        assert response1.status_code == 200

        # 2. Segunda requisição (cache hit)
        response2 = self.client.get(f"/api/dashboard/personalized-kpis/{self.test_user_id}")
        assert response2.status_code == 200

        # Verificar que dados são consistentes
        assert response1.json() == response2.json()

    def test_bcb_integration(self):
        """Testa integração com BCB."""

        response = self.client.get("/api/external/bcb/usd-rate")
        assert response.status_code == 200

        data = response.json()
        assert "rate" in data
        assert "source" in data
        assert data["source"] == "BCB"
```

#### **Testes de Performance**
```python
# tests/performance/test_personalization_performance.py
import asyncio
import time
from concurrent.futures import ThreadPoolExecutor

class TestPersonalizationPerformance:
    """Testes de performance da personalização."""

    async def test_concurrent_profile_detection(self):
        """Testa detecção de perfil com múltiplos usuários."""

        user_ids = [f"user_{i}" for i in range(100)]

        start_time = time.time()

        # Executar detecções em paralelo
        tasks = [self.detect_profile(user_id) for user_id in user_ids]
        results = await asyncio.gather(*tasks)

        end_time = time.time()
        total_time = end_time - start_time

        # Verificar performance
        assert total_time < 5.0  # Menos de 5 segundos para 100 usuários
        assert len(results) == 100
        assert all(result is not None for result in results)

    async def test_kpi_calculation_performance(self):
        """Testa performance de cálculo de KPIs personalizados."""

        start_time = time.time()

        # Calcular KPIs para múltiplos usuários
        tasks = []
        for i in range(50):
            user_id = f"user_{i}"
            task = self.calculate_personalized_kpis(user_id)
            tasks.append(task)

        results = await asyncio.gather(*tasks)

        end_time = time.time()
        total_time = end_time - start_time

        # Performance target: < 3 segundos para 50 usuários
        assert total_time < 3.0
        assert len(results) == 50
```

## 📋 Checklist de Entrega

### **Critérios de Aceitação**
```yaml
criterios_aceitacao:
  funcionalidades_core:
    - ✅ 4_novos_kpis_funcionais_calculando
    - ✅ sistema_deteccao_perfil_automatico
    - ✅ dashboard_personalizado_por_perfil
    - ✅ cache_personalizado_operacional
    - ✅ integracao_bcb_funcional

  compatibilidade:
    - ✅ todos_endpoints_existentes_funcionando
    - ✅ langgraph_workflow_inalterado
    - ✅ frontend_existente_compativel
    - ✅ zero_breaking_changes

  performance:
    - ✅ tempo_resposta_kpis_personalizados_500ms
    - ✅ deteccao_perfil_200ms
    - ✅ cache_hit_rate_80_percent
    - ✅ uptime_99_9_percent

  qualidade:
    - ✅ cobertura_testes_90_percent
    - ✅ zero_bugs_criticos
    - ✅ documentacao_completa
    - ✅ logs_estruturados_implementados
```

### **Deliverables Finais**
```yaml
deliverables:
  backend:
    - profile_models.py
    - profile_detector.py
    - kpi_recommender.py
    - bcb_integration.py
    - personalized_cache.py
    - profile_api.py
    - dashboard_api_extended.py

  frontend:
    - usePersonalization.ts
    - ProfileSetup.tsx
    - KpiSelector.tsx
    - KpiBentoGrid_extended.tsx
    - profileService.ts

  database:
    - migration_user_profiles.sql
    - migration_kpi_definitions.sql
    - migration_operational_costs.sql
    - seed_data_kpi_definitions.sql

  documentacao:
    - api_documentation_profile_endpoints
    - user_guide_personalization
    - deployment_guide_phase_1
    - troubleshooting_guide
```

---

*Este PRD detalhado garante implementação segura, performática e totalmente compatível da Fase 1, estabelecendo a fundação sólida para as próximas fases do roadmap DataHero4.*

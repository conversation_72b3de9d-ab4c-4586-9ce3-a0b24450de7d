# KPIs Personalizados por Perfil - DataHero4

## 🎯 Filosofia: KPIs Específicos por Função

### **Problema Identificado**
- KPIs genéricos não atendem necessidades específicas de cada função
- Cada perfil tem objetivos, responsabilidades e métricas de sucesso diferentes
- Sistema atual mostra os mesmos KPIs para todos os usuários

### **Solução: Personalização Inteligente**
- KPIs específicos por função e nível hierárquico
- Dashboards adaptativos baseados no perfil do usuário
- Sistema de recomendação de KPIs baseado em comportamento
- Configuração automática com possibilidade de customização manual

## 👥 Mapeamento de Perfis e KPIs Críticos

### **CEO/Diretoria (Visão Estratégica)**
```yaml
perfil_ceo:
  foco: "Visão estratégica, rentabilidade, crescimento, risco sistêmico"
  frequencia_consulta: "Diário (manhã) + alertas críticos"
  
  kpis_criticos:
    rentabilidade_geral:
      - margem_liquida_consolidada
      - roe_return_on_equity
      - receita_total_mensal
      - crescimento_receita_yoy
    
    posicao_mercado:
      - market_share_estimado
      - volume_vs_concorrentes
      - nps_consolidado
      - brand_awareness
    
    risco_estrategico:
      - adequacao_capital_regulatorio
      - concentracao_top10_clientes
      - exposicao_maxima_consolidada
      - stress_test_resultado
    
    crescimento:
      - cac_customer_acquisition_cost
      - ltv_lifetime_value
      - taxa_crescimento_clientes
      - expansao_geografica_progresso

  alertas_criticos:
    - margem_liquida < 15%
    - adequacao_capital < limite_regulatorio
    - concentracao_cliente > 25%
    - nps < 50
```

### **CFO/Diretor Financeiro (Visão Financeira)**
```yaml
perfil_cfo:
  foco: "Rentabilidade, fluxo de caixa, controle de custos, compliance financeiro"
  frequencia_consulta: "Diário + relatórios semanais"
  
  kpis_criticos:
    rentabilidade_detalhada:
      - margem_bruta_por_produto
      - margem_liquida_por_segmento
      - ebitda_mensal
      - custo_operacional_por_transacao
    
    fluxo_caixa:
      - cash_flow_operacional
      - working_capital_ratio
      - dias_recebimento_medio
      - liquidez_imediata
    
    eficiencia_financeira:
      - roe_return_on_equity
      - roa_return_on_assets
      - debt_to_equity_ratio
      - capital_efficiency_ratio
    
    compliance_financeiro:
      - adequacao_capital_basileia
      - provisoes_perdas_credito
      - auditoria_compliance_score
      - regulatory_capital_ratio

  alertas_criticos:
    - cash_flow_negativo > 3_dias
    - working_capital < limite_minimo
    - adequacao_capital < 12%
    - custo_por_transacao > benchmark + 20%
```

### **Gerente de Risco (Visão de Risco)**
```yaml
perfil_gerente_risco:
  foco: "Exposição, limites, compliance, detecção de anomalias"
  frequencia_consulta: "Tempo real + relatórios diários"
  
  kpis_criticos:
    exposicao_risco:
      - var_diario_por_moeda
      - exposicao_liquida_consolidada
      - stress_test_cenarios
      - correlacao_posicoes
    
    limites_controle:
      - utilizacao_limites_cliente
      - utilizacao_limites_moeda
      - limite_contraparte_utilizacao
      - overnight_position_limits
    
    qualidade_credito:
      - default_rate_clientes
      - aging_receivables
      - credit_score_medio_carteira
      - provisao_devedores_duvidosos
    
    compliance_risco:
      - aml_alerts_pendentes
      - kyc_completion_rate
      - suspicious_activity_reports
      - regulatory_breaches_count

  alertas_tempo_real:
    - var_diario > limite_aprovado
    - exposicao_moeda > 80%_limite
    - cliente_excedeu_limite
    - transacao_suspeita_detectada
```

### **Gerente Comercial/Vendas (Visão Comercial)**
```yaml
perfil_gerente_comercial:
  foco: "Aquisição, retenção, performance de vendas, satisfação cliente"
  frequencia_consulta: "Diário + relatórios semanais de equipe"
  
  kpis_criticos:
    performance_vendas:
      - volume_vendas_mensal
      - numero_novos_clientes
      - ticket_medio_por_vendedor
      - conversao_leads_clientes
    
    relacionamento_cliente:
      - nps_por_segmento
      - churn_rate_mensal
      - upselling_success_rate
      - tempo_resposta_cliente
    
    eficiencia_comercial:
      - cac_por_canal_aquisicao
      - ltv_cac_ratio
      - produtividade_vendedor
      - pipeline_conversion_rate
    
    satisfacao_servico:
      - tempo_onboarding_cliente
      - reclamacoes_por_1000_transacoes
      - resolucao_primeira_chamada
      - satisfaction_score_atendimento

  alertas_comerciais:
    - churn_rate > 5%_mensal
    - nps_segmento < 60
    - pipeline_conversion < 20%
    - cac > ltv/3
```

### **Gerente de Operações (Visão Operacional)**
```yaml
perfil_gerente_operacoes:
  foco: "Eficiência, qualidade, uptime, custos operacionais"
  frequencia_consulta: "Tempo real + relatórios diários"
  
  kpis_criticos:
    eficiencia_operacional:
      - tempo_processamento_medio
      - throughput_transacoes_hora
      - custo_operacional_unitario
      - produtividade_equipe
    
    qualidade_servico:
      - uptime_sistema_percentual
      - taxa_erro_transacoes
      - sla_compliance_rate
      - first_call_resolution
    
    recursos_capacidade:
      - utilizacao_capacidade_sistema
      - fila_processamento_tamanho
      - recursos_humanos_utilizacao
      - peak_load_handling
    
    melhoria_continua:
      - automation_rate_processos
      - tempo_resolucao_incidentes
      - suggestions_implemented
      - process_improvement_score

  alertas_operacionais:
    - uptime < 99.9%
    - tempo_processamento > sla + 50%
    - fila_processamento > capacidade_80%
    - taxa_erro > 0.1%
```

### **Analista/Trader (Visão Tática)**
```yaml
perfil_analista_trader:
  foco: "Spreads, volatilidade, oportunidades, execução"
  frequencia_consulta: "Tempo real contínuo"
  
  kpis_criticos:
    performance_trading:
      - spread_realizado_vs_target
      - pnl_diario_por_moeda
      - volume_executado_vs_planejado
      - slippage_medio_execucao
    
    oportunidades_mercado:
      - volatilidade_implicita_vs_realizada
      - arbitragem_opportunities
      - spread_vs_concorrentes
      - volume_mercado_share
    
    risco_posicao:
      - posicao_liquida_tempo_real
      - greeks_portfolio_delta
      - overnight_exposure
      - correlation_risk_metrics
    
    execucao_qualidade:
      - fill_rate_ordens
      - tempo_execucao_medio
      - rejection_rate_ordens
      - best_execution_compliance

  alertas_tempo_real:
    - posicao_excede_limite_intraday
    - spread_fora_banda_target
    - volatilidade_anormal_detectada
    - oportunidade_arbitragem_disponivel
```

### **Compliance Officer (Visão Regulatória)**
```yaml
perfil_compliance:
  foco: "Conformidade, relatórios, auditoria, regulamentação"
  frequencia_consulta: "Diário + relatórios regulatórios"
  
  kpis_criticos:
    conformidade_regulatoria:
      - kyc_aml_compliance_rate
      - regulatory_reports_on_time
      - audit_findings_resolved
      - policy_adherence_score
    
    monitoramento_transacoes:
      - suspicious_transactions_flagged
      - false_positive_rate_aml
      - transaction_monitoring_coverage
      - sanctions_screening_hits
    
    reporting_qualidade:
      - regulatory_filing_accuracy
      - report_submission_timeliness
      - data_quality_score
      - exception_reporting_rate
    
    treinamento_cultura:
      - compliance_training_completion
      - policy_acknowledgment_rate
      - whistleblower_reports_handled
      - compliance_culture_survey

  alertas_compliance:
    - regulatory_deadline_approaching
    - suspicious_pattern_detected
    - policy_violation_reported
    - audit_finding_overdue
```

## 🤖 Sistema de Personalização Automática

### **Arquitetura de Perfis Dinâmicos**
```python
# apps/backend/src/services/profile_kpi_service.py
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum

class UserRole(Enum):
    CEO = "ceo"
    CFO = "cfo"
    RISK_MANAGER = "risk_manager"
    COMMERCIAL_MANAGER = "commercial_manager"
    OPERATIONS_MANAGER = "operations_manager"
    ANALYST_TRADER = "analyst_trader"
    COMPLIANCE_OFFICER = "compliance_officer"

@dataclass
class KPIDefinition:
    id: str
    name: str
    description: str
    formula: str
    criticality: str  # CRITICAL, HIGH, MEDIUM, LOW
    frequency: str    # real_time, hourly, daily, weekly, monthly
    category: str
    alert_threshold: Optional[Dict[str, Any]] = None
    benchmark_source: Optional[str] = None

@dataclass
class UserProfile:
    user_id: str
    role: UserRole
    department: str
    seniority_level: str  # junior, senior, manager, director, c_level
    custom_preferences: Dict[str, Any]
    usage_patterns: Dict[str, Any]
    last_updated: str

class ProfileKPIService:
    """Serviço de personalização de KPIs por perfil"""
    
    def __init__(self):
        self.role_kpi_mapping = self._load_role_kpi_mapping()
        self.user_behavior_analyzer = UserBehaviorAnalyzer()
    
    def get_personalized_kpis(self, user_profile: UserProfile) -> List[KPIDefinition]:
        """Retorna KPIs personalizados para o perfil do usuário"""
        
        # 1. KPIs base por função
        base_kpis = self._get_base_kpis_for_role(user_profile.role)
        
        # 2. Ajustes por senioridade
        adjusted_kpis = self._adjust_for_seniority(base_kpis, user_profile.seniority_level)
        
        # 3. Personalização baseada em comportamento
        personalized_kpis = self._personalize_based_on_behavior(
            adjusted_kpis, user_profile.usage_patterns
        )
        
        # 4. Aplicar preferências customizadas
        final_kpis = self._apply_custom_preferences(
            personalized_kpis, user_profile.custom_preferences
        )
        
        return final_kpis
    
    def _get_base_kpis_for_role(self, role: UserRole) -> List[KPIDefinition]:
        """KPIs base por função"""
        
        role_kpis = {
            UserRole.CEO: [
                KPIDefinition(
                    id="margem_liquida_consolidada",
                    name="Margem Líquida Consolidada",
                    description="Rentabilidade líquida após todos os custos",
                    formula="(Receita Total - Custos Totais) / Receita Total * 100",
                    criticality="CRITICAL",
                    frequency="daily",
                    category="rentabilidade",
                    alert_threshold={"min": 15.0, "target": 25.0}
                ),
                KPIDefinition(
                    id="market_share_estimado",
                    name="Market Share Estimado",
                    description="Participação estimada no mercado de câmbio",
                    formula="Volume Empresa / Volume Mercado Total * 100",
                    criticality="HIGH",
                    frequency="monthly",
                    category="posicao_mercado",
                    benchmark_source="industry_reports"
                ),
                # ... mais KPIs do CEO
            ],
            
            UserRole.RISK_MANAGER: [
                KPIDefinition(
                    id="var_diario_consolidado",
                    name="VaR Diário Consolidado",
                    description="Value at Risk diário com 95% de confiança",
                    formula="VaR calculation based on historical simulation",
                    criticality="CRITICAL",
                    frequency="real_time",
                    category="exposicao_risco",
                    alert_threshold={"max": 1000000}  # R$ 1M
                ),
                KPIDefinition(
                    id="utilizacao_limites_consolidada",
                    name="Utilização de Limites Consolidada",
                    description="Percentual de utilização dos limites de risco",
                    formula="Exposição Atual / Limite Aprovado * 100",
                    criticality="CRITICAL",
                    frequency="real_time",
                    category="limites_controle",
                    alert_threshold={"warning": 80.0, "critical": 95.0}
                ),
                # ... mais KPIs do Risk Manager
            ],
            
            # ... outros perfis
        }
        
        return role_kpis.get(role, [])
    
    def _adjust_for_seniority(
        self, 
        base_kpis: List[KPIDefinition], 
        seniority: str
    ) -> List[KPIDefinition]:
        """Ajusta KPIs baseado no nível de senioridade"""
        
        if seniority in ["director", "c_level"]:
            # Adicionar KPIs estratégicos
            return self._add_strategic_kpis(base_kpis)
        elif seniority == "manager":
            # Adicionar KPIs táticos
            return self._add_tactical_kpis(base_kpis)
        else:
            # Manter KPIs operacionais
            return base_kpis
    
    def recommend_additional_kpis(
        self, 
        user_profile: UserProfile,
        current_kpis: List[str]
    ) -> List[KPIDefinition]:
        """Recomenda KPIs adicionais baseado no perfil e uso"""
        
        # Análise de gaps
        missing_categories = self._identify_missing_categories(
            user_profile.role, current_kpis
        )
        
        # KPIs complementares
        complementary_kpis = self._find_complementary_kpis(current_kpis)
        
        # Baseado em comportamento de usuários similares
        similar_user_kpis = self._get_similar_user_kpis(user_profile)
        
        recommendations = []
        recommendations.extend(missing_categories)
        recommendations.extend(complementary_kpis)
        recommendations.extend(similar_user_kpis)
        
        # Remover duplicatas e ordenar por relevância
        return self._deduplicate_and_rank(recommendations, user_profile)
```

### **Sistema de Aprendizado de Comportamento**
```python
# apps/backend/src/services/user_behavior_analyzer.py
from typing import Dict, List, Any
from datetime import datetime, timedelta
import numpy as np

class UserBehaviorAnalyzer:
    """Analisa comportamento do usuário para personalização"""
    
    def analyze_usage_patterns(self, user_id: str) -> Dict[str, Any]:
        """Analisa padrões de uso do usuário"""
        
        # Coleta dados de uso dos últimos 30 dias
        usage_data = self._get_usage_data(user_id, days=30)
        
        patterns = {
            "most_viewed_kpis": self._get_most_viewed_kpis(usage_data),
            "peak_usage_hours": self._get_peak_hours(usage_data),
            "preferred_timeframes": self._get_preferred_timeframes(usage_data),
            "drill_down_frequency": self._get_drill_down_patterns(usage_data),
            "alert_interaction_rate": self._get_alert_interactions(usage_data),
            "dashboard_customizations": self._get_customization_patterns(usage_data)
        }
        
        return patterns
    
    def predict_kpi_relevance(
        self, 
        user_profile: UserProfile, 
        kpi_id: str
    ) -> float:
        """Prediz relevância de um KPI para o usuário (0-1)"""
        
        # Fatores de relevância
        role_relevance = self._calculate_role_relevance(user_profile.role, kpi_id)
        usage_relevance = self._calculate_usage_relevance(user_profile.user_id, kpi_id)
        peer_relevance = self._calculate_peer_relevance(user_profile, kpi_id)
        
        # Peso combinado
        relevance_score = (
            role_relevance * 0.4 +
            usage_relevance * 0.4 +
            peer_relevance * 0.2
        )
        
        return min(1.0, max(0.0, relevance_score))
    
    def suggest_dashboard_layout(self, user_profile: UserProfile) -> Dict[str, Any]:
        """Sugere layout de dashboard baseado no perfil"""
        
        if user_profile.role == UserRole.CEO:
            return {
                "layout": "executive_summary",
                "card_size": "large",
                "charts_preferred": ["trend_lines", "gauges", "scorecards"],
                "update_frequency": "daily",
                "alert_prominence": "high_level_only"
            }
        elif user_profile.role == UserRole.RISK_MANAGER:
            return {
                "layout": "detailed_monitoring",
                "card_size": "medium",
                "charts_preferred": ["heatmaps", "real_time_lines", "tables"],
                "update_frequency": "real_time",
                "alert_prominence": "all_levels"
            }
        # ... outros perfis
```

## 📊 Roadmap de Implementação por Perfil

### **Fase 1: Perfis Críticos (6 semanas)**
```yaml
fase_1_perfis_criticos:
  objetivo: "Implementar personalização para perfis mais críticos"
  
  perfis_prioritarios:
    - ceo_diretoria
    - gerente_risco
    - gerente_operacoes
  
  semanas_1_2:
    - definir_kpis_base_por_perfil
    - criar_sistema_profile_detection
    - implementar_kpis_ceo
  
  semanas_3_4:
    - implementar_kpis_risk_manager
    - sistema_alertas_personalizados
    - dashboard_adaptativo_basico
  
  semanas_5_6:
    - implementar_kpis_operations
    - testes_integracao_perfis
    - refinamento_baseado_feedback
```

### **Fase 2: Perfis Comerciais e Analíticos (4 semanas)**
```yaml
fase_2_comercial_analitico:
  objetivo: "Expandir para perfis comerciais e analíticos"
  
  perfis_fase_2:
    - gerente_comercial
    - analista_trader
  
  semanas_1_2:
    - implementar_kpis_comerciais
    - metricas_performance_vendas
    - sistema_recomendacao_kpis
  
  semanas_3_4:
    - implementar_kpis_trading
    - alertas_tempo_real_trading
    - integracao_dados_mercado
```

### **Fase 3: Perfis Especializados (4 semanas)**
```yaml
fase_3_especializados:
  objetivo: "Completar com perfis especializados"
  
  perfis_fase_3:
    - cfo_financeiro
    - compliance_officer
  
  semanas_1_2:
    - implementar_kpis_financeiros
    - relatorios_regulatorios
    - compliance_dashboard
  
  semanas_3_4:
    - sistema_aprendizado_comportamento
    - personalizacao_automatica
    - otimizacao_performance
```

### **Fase 4: Inteligência Adaptativa (3 semanas)**
```yaml
fase_4_inteligencia:
  objetivo: "Sistema de aprendizado e adaptação automática"
  
  componentes:
    - user_behavior_analyzer
    - kpi_recommendation_engine
    - adaptive_dashboard_layout
    - peer_comparison_system
  
  semanas_1_2:
    - implementar_behavior_analyzer
    - sistema_recomendacao_ml
  
  semana_3:
    - testes_sistema_completo
    - otimizacao_performance
    - documentacao_final
```

## 🎯 Casos de Uso Práticos por Perfil

### **Caso 1: CEO às 8h da manhã**
```yaml
cenario: "CEO abre dashboard para briefing matinal"
contexto: "Precisa de visão executiva rápida antes de reuniões"

dashboard_personalizado:
  layout: "executive_summary"
  kpis_exibidos:
    - margem_liquida: "23.5% ↗️ ****%"
    - volume_mensal: "R$ 45.2M ↗️ ****%"
    - nps_consolidado: "72 ↘️ -3 pontos"
    - adequacao_capital: "18.2% ✅ Acima do mínimo"

  alertas_relevantes:
    - "NPS em queda - investigar satisfação clientes"
    - "Volume crescendo acima da meta (****%)"

  tempo_visualizacao: "< 30 segundos"
  proxima_acao: "Drill-down em NPS se necessário"
```

### **Caso 2: Gerente de Risco durante pregão**
```yaml
cenario: "Monitoramento contínuo durante horário de operação"
contexto: "Precisa detectar riscos em tempo real"

dashboard_personalizado:
  layout: "real_time_monitoring"
  refresh_rate: "5 segundos"

  kpis_tempo_real:
    - var_diario: "R$ 850k / R$ 1M ⚠️ 85% do limite"
    - exposicao_usd: "USD 2.3M ↗️ Crescendo"
    - utilizacao_limites: "Cliente ABC: 92% ⚠️"
    - transacoes_suspeitas: "3 alertas pendentes"

  alertas_automaticos:
    - "Cliente ABC próximo do limite (92%)"
    - "Exposição USD crescendo rapidamente"

  acoes_disponiveis:
    - "Reduzir limite Cliente ABC"
    - "Hedge automático USD"
    - "Investigar transações suspeitas"
```

### **Caso 3: Gerente Comercial na segunda-feira**
```yaml
cenario: "Análise semanal de performance comercial"
contexto: "Planejamento da semana e acompanhamento de metas"

dashboard_personalizado:
  layout: "commercial_performance"
  periodo: "última_semana + meta_mensal"

  kpis_comerciais:
    - novos_clientes: "12 clientes ↗️ Meta: 50/mês"
    - conversao_leads: "18.5% ↘️ -2.3%"
    - ticket_medio_novos: "R$ 15.2k ↗️ +12%"
    - churn_rate: "3.2% ✅ Dentro da meta"

  analise_equipe:
    - vendedor_destaque: "João Silva - 4 novos clientes"
    - vendedor_atencao: "Maria Santos - 0 conversões"

  acoes_sugeridas:
    - "Investigar queda na conversão de leads"
    - "Replicar estratégia do João Silva"
    - "Treinamento para Maria Santos"
```

## 🔄 Sistema de Adaptação Automática

### **Algoritmo de Personalização Inteligente**
```python
# apps/backend/src/services/adaptive_kpi_engine.py
from typing import Dict, List, Any, Tuple
import numpy as np
from sklearn.cluster import KMeans
from datetime import datetime, timedelta

class AdaptiveKPIEngine:
    """Engine de adaptação automática de KPIs"""

    def __init__(self):
        self.user_profiles = {}
        self.kpi_usage_matrix = {}
        self.peer_groups = {}

    def adapt_dashboard_realtime(self, user_id: str) -> Dict[str, Any]:
        """Adapta dashboard em tempo real baseado no contexto"""

        current_context = self._get_current_context(user_id)
        user_profile = self._get_user_profile(user_id)

        # Detectar contexto atual
        context_type = self._detect_context_type(current_context)

        # Adaptar KPIs baseado no contexto
        if context_type == "morning_briefing":
            return self._get_morning_briefing_kpis(user_profile)
        elif context_type == "crisis_mode":
            return self._get_crisis_mode_kpis(user_profile)
        elif context_type == "end_of_day":
            return self._get_eod_summary_kpis(user_profile)
        else:
            return self._get_standard_kpis(user_profile)

    def _detect_context_type(self, context: Dict[str, Any]) -> str:
        """Detecta tipo de contexto baseado em padrões"""

        hour = datetime.now().hour
        day_of_week = datetime.now().weekday()

        # Padrões temporais
        if 7 <= hour <= 9 and day_of_week < 5:  # Manhã, dias úteis
            return "morning_briefing"
        elif 17 <= hour <= 19 and day_of_week < 5:  # Final do dia
            return "end_of_day"

        # Padrões de alerta
        critical_alerts = context.get("critical_alerts", 0)
        if critical_alerts > 3:
            return "crisis_mode"

        # Padrões de mercado
        market_volatility = context.get("market_volatility", 0)
        if market_volatility > 0.8:  # Alta volatilidade
            return "high_volatility"

        return "standard"

    def learn_from_user_behavior(self, user_id: str, interaction_data: Dict[str, Any]):
        """Aprende com comportamento do usuário"""

        # Registrar interação
        self._record_interaction(user_id, interaction_data)

        # Atualizar perfil do usuário
        self._update_user_profile(user_id, interaction_data)

        # Recalcular recomendações
        self._recalculate_recommendations(user_id)

    def _record_interaction(self, user_id: str, interaction: Dict[str, Any]):
        """Registra interação do usuário"""

        interaction_record = {
            "timestamp": datetime.now().isoformat(),
            "user_id": user_id,
            "kpi_viewed": interaction.get("kpi_id"),
            "time_spent": interaction.get("time_spent", 0),
            "drill_down": interaction.get("drill_down", False),
            "action_taken": interaction.get("action_taken"),
            "context": interaction.get("context", {})
        }

        # Salvar no banco de dados
        self._save_interaction(interaction_record)

    def recommend_kpi_additions(self, user_id: str) -> List[Dict[str, Any]]:
        """Recomenda novos KPIs baseado em análise de gaps"""

        user_profile = self._get_user_profile(user_id)
        current_kpis = self._get_user_current_kpis(user_id)

        # Encontrar usuários similares
        similar_users = self._find_similar_users(user_profile)

        # KPIs populares entre usuários similares
        popular_kpis = self._get_popular_kpis_among_peers(similar_users)

        # KPIs que o usuário não tem
        missing_kpis = [kpi for kpi in popular_kpis if kpi not in current_kpis]

        # Calcular score de recomendação
        recommendations = []
        for kpi in missing_kpis:
            score = self._calculate_recommendation_score(user_profile, kpi)
            if score > 0.6:  # Threshold de relevância
                recommendations.append({
                    "kpi_id": kpi["id"],
                    "name": kpi["name"],
                    "relevance_score": score,
                    "reason": self._generate_recommendation_reason(user_profile, kpi),
                    "category": kpi["category"]
                })

        # Ordenar por relevância
        return sorted(recommendations, key=lambda x: x["relevance_score"], reverse=True)
```

### **Sistema de Detecção de Perfil Automático**
```python
# apps/backend/src/services/profile_detector.py
from typing import Dict, Any, Optional
import re
from datetime import datetime

class ProfileDetector:
    """Detecta perfil do usuário automaticamente"""

    def __init__(self):
        self.role_patterns = self._load_role_patterns()
        self.behavior_classifiers = self._load_behavior_classifiers()

    def detect_user_role(self, user_data: Dict[str, Any]) -> UserRole:
        """Detecta função do usuário baseado em dados disponíveis"""

        # 1. Detecção por título/cargo
        title_role = self._detect_by_title(user_data.get("job_title", ""))
        if title_role:
            return title_role

        # 2. Detecção por departamento
        dept_role = self._detect_by_department(user_data.get("department", ""))
        if dept_role:
            return dept_role

        # 3. Detecção por padrão de uso
        usage_role = self._detect_by_usage_pattern(user_data.get("usage_history", []))
        if usage_role:
            return usage_role

        # 4. Detecção por permissões/acessos
        permission_role = self._detect_by_permissions(user_data.get("permissions", []))
        if permission_role:
            return permission_role

        # Default: Analista
        return UserRole.ANALYST_TRADER

    def _detect_by_title(self, job_title: str) -> Optional[UserRole]:
        """Detecta por título do cargo"""

        title_lower = job_title.lower()

        ceo_patterns = ["ceo", "chief executive", "diretor presidente", "presidente"]
        if any(pattern in title_lower for pattern in ceo_patterns):
            return UserRole.CEO

        cfo_patterns = ["cfo", "chief financial", "diretor financeiro", "gerente financeiro"]
        if any(pattern in title_lower for pattern in cfo_patterns):
            return UserRole.CFO

        risk_patterns = ["risco", "risk", "gerente de risco", "analista de risco"]
        if any(pattern in title_lower for pattern in risk_patterns):
            return UserRole.RISK_MANAGER

        commercial_patterns = ["comercial", "vendas", "sales", "business development"]
        if any(pattern in title_lower for pattern in commercial_patterns):
            return UserRole.COMMERCIAL_MANAGER

        operations_patterns = ["operações", "operations", "operacional"]
        if any(pattern in title_lower for pattern in operations_patterns):
            return UserRole.OPERATIONS_MANAGER

        compliance_patterns = ["compliance", "conformidade", "auditoria"]
        if any(pattern in title_lower for pattern in compliance_patterns):
            return UserRole.COMPLIANCE_OFFICER

        return None

    def _detect_by_usage_pattern(self, usage_history: List[Dict[str, Any]]) -> Optional[UserRole]:
        """Detecta por padrão de uso"""

        if not usage_history:
            return None

        # Analisar KPIs mais acessados
        kpi_access_count = {}
        for usage in usage_history:
            kpi_id = usage.get("kpi_id")
            if kpi_id:
                kpi_access_count[kpi_id] = kpi_access_count.get(kpi_id, 0) + 1

        most_accessed = sorted(kpi_access_count.items(), key=lambda x: x[1], reverse=True)

        # Padrões por tipo de KPI
        if self._has_pattern(most_accessed, ["margem_liquida", "roe", "market_share"]):
            return UserRole.CEO
        elif self._has_pattern(most_accessed, ["var_diario", "exposicao", "limites"]):
            return UserRole.RISK_MANAGER
        elif self._has_pattern(most_accessed, ["uptime", "tempo_processamento", "custo_operacional"]):
            return UserRole.OPERATIONS_MANAGER
        elif self._has_pattern(most_accessed, ["novos_clientes", "conversao", "churn"]):
            return UserRole.COMMERCIAL_MANAGER

        return None

    def auto_configure_dashboard(self, user_id: str) -> Dict[str, Any]:
        """Configura dashboard automaticamente para novo usuário"""

        # Detectar perfil
        user_data = self._get_user_data(user_id)
        detected_role = self.detect_user_role(user_data)

        # Configuração base por perfil
        base_config = self._get_base_dashboard_config(detected_role)

        # Personalizar baseado em contexto da empresa
        company_context = self._get_company_context(user_data.get("company_id"))
        personalized_config = self._personalize_for_company(base_config, company_context)

        # Aplicar configuração
        self._apply_dashboard_config(user_id, personalized_config)

        return {
            "detected_role": detected_role.value,
            "confidence": self._calculate_detection_confidence(user_data, detected_role),
            "config_applied": personalized_config,
            "suggestions": self._generate_onboarding_suggestions(detected_role)
        }
```

### **Interface de Configuração Personalizada**
```typescript
// apps/frontend/src/components/profile/ProfileKPISelector.tsx
import React, { useState, useEffect } from 'react';
import { UserRole, KPIDefinition } from '@/types/profile';

interface ProfileKPISelectorProps {
  userRole: UserRole;
  currentKPIs: string[];
  onKPISelectionChange: (kpis: string[]) => void;
}

export const ProfileKPISelector: React.FC<ProfileKPISelectorProps> = ({
  userRole,
  currentKPIs,
  onKPISelectionChange
}) => {
  const [availableKPIs, setAvailableKPIs] = useState<KPIDefinition[]>([]);
  const [recommendations, setRecommendations] = useState<KPIDefinition[]>([]);
  const [selectedKPIs, setSelectedKPIs] = useState<string[]>(currentKPIs);

  useEffect(() => {
    // Carregar KPIs disponíveis para o perfil
    fetchAvailableKPIs(userRole).then(setAvailableKPIs);

    // Carregar recomendações personalizadas
    fetchKPIRecommendations(userRole).then(setRecommendations);
  }, [userRole]);

  const handleKPIToggle = (kpiId: string) => {
    const newSelection = selectedKPIs.includes(kpiId)
      ? selectedKPIs.filter(id => id !== kpiId)
      : [...selectedKPIs, kpiId];

    setSelectedKPIs(newSelection);
    onKPISelectionChange(newSelection);
  };

  const renderKPICard = (kpi: KPIDefinition, isRecommended: boolean = false) => (
    <div
      key={kpi.id}
      className={`
        p-4 border rounded-lg cursor-pointer transition-all
        ${selectedKPIs.includes(kpi.id)
          ? 'border-blue-500 bg-blue-50'
          : 'border-gray-200 hover:border-gray-300'
        }
        ${isRecommended ? 'ring-2 ring-yellow-400' : ''}
      `}
      onClick={() => handleKPIToggle(kpi.id)}
    >
      <div className="flex items-center justify-between">
        <h3 className="font-semibold">{kpi.name}</h3>
        <div className="flex items-center space-x-2">
          {isRecommended && (
            <span className="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded">
              Recomendado
            </span>
          )}
          <span className={`
            text-xs px-2 py-1 rounded
            ${kpi.criticality === 'CRITICAL'
              ? 'bg-red-100 text-red-800'
              : kpi.criticality === 'HIGH'
              ? 'bg-orange-100 text-orange-800'
              : 'bg-gray-100 text-gray-800'
            }
          `}>
            {kpi.criticality}
          </span>
        </div>
      </div>

      <p className="text-sm text-gray-600 mt-2">{kpi.description}</p>

      <div className="flex items-center justify-between mt-3 text-xs text-gray-500">
        <span>Categoria: {kpi.category}</span>
        <span>Frequência: {kpi.frequency}</span>
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Recomendações */}
      {recommendations.length > 0 && (
        <div>
          <h2 className="text-lg font-semibold mb-4 flex items-center">
            <span className="mr-2">⭐</span>
            Recomendados para seu perfil
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {recommendations.map(kpi => renderKPICard(kpi, true))}
          </div>
        </div>
      )}

      {/* KPIs por categoria */}
      <div>
        <h2 className="text-lg font-semibold mb-4">Todos os KPIs disponíveis</h2>
        {Object.entries(groupKPIsByCategory(availableKPIs)).map(([category, kpis]) => (
          <div key={category} className="mb-6">
            <h3 className="font-medium text-gray-700 mb-3 capitalize">
              {category.replace('_', ' ')}
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {kpis.map(kpi => renderKPICard(kpi))}
            </div>
          </div>
        ))}
      </div>

      {/* Resumo da seleção */}
      <div className="bg-gray-50 p-4 rounded-lg">
        <h3 className="font-medium mb-2">Resumo da seleção</h3>
        <p className="text-sm text-gray-600">
          {selectedKPIs.length} KPIs selecionados para seu dashboard
        </p>
        <div className="mt-2 flex flex-wrap gap-2">
          {selectedKPIs.map(kpiId => {
            const kpi = availableKPIs.find(k => k.id === kpiId);
            return kpi ? (
              <span
                key={kpiId}
                className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded"
              >
                {kpi.name}
              </span>
            ) : null;
          })}
        </div>
      </div>
    </div>
  );
};
```

## 📈 Métricas de Sucesso da Personalização

### **KPIs do Sistema de Personalização**
```yaml
metricas_personalizacao:
  adocao:
    - taxa_usuarios_personalizaram_dashboard: "> 80%"
    - tempo_medio_primeira_personalizacao: "< 5 minutos"
    - usuarios_que_mantiveram_personalizacao: "> 90%"

  engajamento:
    - tempo_medio_sessao_pos_personalizacao: "+40%"
    - frequencia_acesso_dashboard: "+60%"
    - interacao_com_kpis_personalizados: "+80%"

  eficacia:
    - precisao_recomendacoes_kpi: "> 85%"
    - satisfacao_usuario_personalizacao: "> 4.5/5"
    - reducao_tempo_encontrar_informacao: "-50%"

  negocio:
    - decisoes_baseadas_kpis_personalizados: "+70%"
    - deteccao_precoce_problemas: "+90%"
    - melhoria_performance_kpis_criticos: "+25%"
```

---

*Este sistema de personalização por perfil transforma o DataHero4 em uma plataforma verdadeiramente adaptativa, onde cada usuário vê exatamente os KPIs que importam para sua função, responsabilidades e contexto atual, maximizando a eficácia e o valor da plataforma para cada perfil de usuário.*

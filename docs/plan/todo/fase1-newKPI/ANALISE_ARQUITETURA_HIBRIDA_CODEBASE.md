# Análise: Arquitetura Híbrida com Materialized Views - Codebase DataHero4

## 🔍 Estado Atual do Codebase

### **✅ Componentes Já Implementados**

#### **1. Sistema de Snapshots (PRONTO)**
```python
# apps/backend/generate_snapshot_cron.py - JÁ EXISTE
class SnapshotService:
    def generate_daily_snapshot(self, client_id: str):
        # Sistema de snapshots diários às 3AM já implementado
        # Armazena KPIs pré-calculados no PostgreSQL
        pass

# Tabela já existe no codebase
CREATE TABLE kpi_snapshots (
    id SERIAL PRIMARY KEY,
    kpi_id VARCHAR(50) NOT NULL,
    client_id VARCHAR(50) NOT NULL,
    value DECIMAL(15,2),
    metadata JSONB,
    created_at TIMESTAMP DEFAULT NOW()
);
```

#### **2. Cache Multi-Camada (PRONTO)**
```python
# apps/backend/src/caching/unified_cache_system.py - JÁ EXISTE
class UnifiedCacheSystem:
    def __init__(self, max_size: int = 1000):
        self._cache = OrderedDict()  # LRU cache
        self.enable_stats = True
        # TTL inteligente já implementado
        # Estatísticas de hit/miss já implementadas
        
# apps/backend/src/caching/llm_response_cache.py - JÁ EXISTE
class LLMResponseCache:
    # Cache específico para respostas LLM
    # TTL configurável por provider/model
```

#### **3. Query Cache com Embeddings (PRONTO)**
```python
# apps/backend/src/models/learning_models.py - JÁ EXISTE
class QueryCache(Base):
    __tablename__ = 'query_cache'
    
    question = Column(Text, nullable=False)
    sql_query = Column(Text, nullable=False)
    embedding = Column(LargeBinary)  # Embeddings já implementados
    cached_results = Column(JSON)
    business_analysis = Column(JSON)
    feedback_score = Column(Float)
    # Sistema de feedback já implementado
```

#### **4. KPI Models e Repository (PRONTO)**
```python
# apps/backend/src/models/kpi_models.py - JÁ EXISTE
class KpiCalculationResult(SQLModel):
    kpi_id: str
    current_value: float
    previous_value: Optional[float]
    change_percent: Optional[float]
    chart_data: Optional[List[Dict[str, Any]]]
    # Estrutura completa para KPIs já definida

class KpiRepository:
    # Repository pattern já implementado
    # Acesso a dados via SQLModel
```

### **🔧 Componentes Que Precisam de Extensão**

#### **1. Snapshots Otimizados para Perfis**
```python
# EXTENSÃO NECESSÁRIA: apps/backend/src/models/kpi_snapshots_v2.py
class KpiSnapshotV2(SQLModel, table=True):
    """Snapshots otimizados com dimensões por perfil"""
    __tablename__ = "kpi_snapshots_v2"
    
    client_id: str = Field(primary_key=True)
    kpi_id: str = Field(primary_key=True)
    user_profile: str = Field(primary_key=True)  # NOVO
    period_type: str = Field(primary_key=True)
    period_date: date = Field(primary_key=True)
    
    # Dimensões flexíveis por perfil
    dimensions: Dict[str, Any] = Field(default_factory=dict)  # {currency: 'USD', region: 'BR'}
    metrics: Dict[str, Any] = Field(default_factory=dict)     # {value: 1000, change: 0.15}
    
    created_at: datetime = Field(default_factory=datetime.now)
```

#### **2. Query Router Inteligente**
```python
# NOVO: apps/backend/src/services/smart_query_router.py
class SmartQueryRouter:
    """Router que decide camada de dados baseado no perfil"""
    
    def __init__(self):
        self.snapshot_service = SnapshotService()  # JÁ EXISTE
        self.cache_system = UnifiedCacheSystem()   # JÁ EXISTE
        self.kpi_repository = KpiRepository()      # JÁ EXISTE
    
    def get_kpi_data(self, kpi_id: str, timeframe: str, user_profile: str) -> Dict[str, Any]:
        """Roteamento inteligente fail-fast"""
        
        # 1. Tenta snapshot se aplicável
        if self.is_snapshot_eligible(kpi_id, timeframe, user_profile):
            snapshot_data = self.get_from_snapshot(kpi_id, user_profile)
            if snapshot_data:
                return snapshot_data
            # Fail fast se snapshot deveria existir mas não existe
            raise Exception(f"Snapshot missing for {kpi_id}, profile {user_profile}")
        
        # 2. Cache quente personalizado
        cache_key = f"kpi:{kpi_id}:{user_profile}:{timeframe}"
        cached = self.cache_system.get("personalized_kpis", cache_key=cache_key)
        if cached:
            return cached
        
        # 3. Query direta otimizada
        result = self.calculate_kpi_direct(kpi_id, timeframe, user_profile)
        
        # Cache resultado com TTL baseado no perfil
        ttl = self.get_profile_ttl(user_profile, kpi_id)
        self.cache_system.set("personalized_kpis", result, ttl=ttl, cache_key=cache_key)
        
        return result
```

## 🏗️ Integração com Plano Fail Fast

### **Arquitetura Híbrida Fail Fast**

#### **Camada 1: Snapshots Críticos (JÁ EXISTE + EXTENSÃO)**
```yaml
snapshots_fail_fast:
  implementacao_atual:
    - generate_snapshot_cron.py: "Sistema diário às 3AM"
    - kpi_snapshots table: "Armazenamento PostgreSQL"
    - SnapshotService: "Geração automática"
  
  extensao_necessaria:
    - kpi_snapshots_v2: "Dimensões por perfil"
    - profile_aware_snapshots: "Snapshots personalizados"
    - fail_fast_validation: "Validação obrigatória snapshots"
  
  fail_fast_rules:
    - snapshot_missing_for_critical_kpi: "FAIL - não usar fallback"
    - snapshot_data_invalid: "FAIL - não usar dados corrompidos"
    - snapshot_too_old: "FAIL - não usar dados desatualizados"
```

#### **Camada 2: Cache Quente Personalizado (EXTENSÃO DO EXISTENTE)**
```python
# EXTENSÃO: apps/backend/src/caching/personalized_cache_system.py
class PersonalizedCacheSystem(UnifiedCacheSystem):  # HERDA DO EXISTENTE
    """Cache personalizado por perfil - fail fast"""
    
    def __init__(self):
        super().__init__()
        self.profile_ttl_map = {
            "CEO": {"critical_kpis": 3600, "standard_kpis": 7200},
            "Risk_Manager": {"critical_kpis": 300, "standard_kpis": 900},
            "Trader": {"critical_kpis": 60, "standard_kpis": 300}
        }
    
    def get_personalized(self, kpi_id: str, user_profile: str, timeframe: str) -> Dict[str, Any]:
        """Cache personalizado - fail fast se perfil inválido"""
        
        if not user_profile or user_profile not in self.profile_ttl_map:
            raise ValueError(f"Invalid user_profile: {user_profile}")
        
        cache_key = f"kpi:{kpi_id}:{user_profile}:{timeframe}"
        cached_data = self.get("personalized_kpis", cache_key=cache_key)
        
        if cached_data:
            # Validar integridade dos dados
            if not self.validate_cached_data(cached_data, kpi_id):
                # Fail fast: dados corrompidos
                self.invalidate("personalized_kpis", cache_key=cache_key)
                raise Exception(f"Corrupted cache data for {kpi_id}")
            
            return cached_data
        
        return None  # Cache miss - não é erro
```

#### **Camada 3: Query Direta Otimizada (NOVO)**
```python
# NOVO: apps/backend/src/services/optimized_query_service.py
class OptimizedQueryService:
    """Query direta com otimizações por perfil"""
    
    def __init__(self):
        self.kpi_repository = KpiRepository()  # JÁ EXISTE
        self.profile_query_optimizer = ProfileQueryOptimizer()
    
    def calculate_kpi_optimized(self, kpi_id: str, user_profile: str, timeframe: str) -> Dict[str, Any]:
        """Cálculo otimizado por perfil - fail fast"""
        
        # Otimizações específicas por perfil
        query_strategy = self.get_profile_query_strategy(user_profile, kpi_id)
        
        if query_strategy == "aggregated":
            # Para CEO: dados agregados rápidos
            return self.calculate_aggregated_kpi(kpi_id, timeframe)
        elif query_strategy == "real_time":
            # Para Trader: dados em tempo real
            return self.calculate_realtime_kpi(kpi_id, timeframe)
        elif query_strategy == "detailed":
            # Para Risk Manager: dados detalhados
            return self.calculate_detailed_kpi(kpi_id, timeframe)
        else:
            raise ValueError(f"Unknown query strategy: {query_strategy}")
```

### **Implementação Branch-Based da Arquitetura Híbrida**

#### **Branch: feature/fase1-arquitetura-hibrida**
```yaml
implementacao_branch:
  aproveitamento_existente:
    - unified_cache_system.py: "Estender sem modificar"
    - generate_snapshot_cron.py: "Adicionar snapshots por perfil"
    - kpi_models.py: "Adicionar modelos personalizados"
    - learning_models.py: "Usar QueryCache existente"
  
  novos_componentes:
    - smart_query_router.py: "Router inteligente"
    - personalized_cache_system.py: "Cache por perfil"
    - optimized_query_service.py: "Queries otimizadas"
    - kpi_snapshots_v2.py: "Snapshots dimensionais"
  
  fail_fast_integration:
    - sem_fallbacks_entre_camadas: "Se snapshot falha, não usar cache"
    - validacao_obrigatoria_dados: "Dados corrompidos = falha"
    - profile_sempre_obrigatorio: "Sem perfil = sem dados"
```

## 📊 Estratégia de Cache por Perfil

### **TTL Inteligente Baseado no Perfil**
```python
# Integração com sistema existente
PROFILE_CACHE_STRATEGY = {
    "CEO": {
        "snapshot_priority": ["margem_liquida", "volume_total", "crescimento_receita"],
        "cache_ttl": 3600,  # 1 hora - dados estratégicos
        "prefetch": True,   # Pre-carregar dados importantes
        "fail_fast": True   # Falhar se dados críticos indisponíveis
    },
    "Risk_Manager": {
        "snapshot_priority": ["var_diario", "exposicao_cambial", "utilizacao_limites"],
        "cache_ttl": 300,   # 5 minutos - dados de risco
        "real_time_fallback": False,  # FAIL FAST - sem fallback
        "fail_fast": True
    },
    "Trader": {
        "snapshot_priority": ["spread_realtime", "volume_hora", "volatilidade"],
        "cache_ttl": 60,    # 1 minuto - dados operacionais
        "streaming_option": True,
        "fail_fast": True
    },
    "CFO": {
        "snapshot_priority": ["margem_liquida", "custo_transacao", "roe"],
        "cache_ttl": 1800,  # 30 minutos - dados financeiros
        "prefetch": True,
        "fail_fast": True
    }
}
```

### **Query Optimization por Perfil**
```python
# Otimizações específicas aproveitando estrutura existente
PROFILE_QUERY_OPTIMIZATION = {
    "CEO": {
        "aggregation_level": "daily",
        "detail_level": "summary",
        "index_hints": ["idx_date_client", "idx_kpi_category"],
        "query_timeout": 5000  # 5 segundos max
    },
    "Risk_Manager": {
        "aggregation_level": "hourly",
        "detail_level": "detailed",
        "index_hints": ["idx_timestamp_risk", "idx_client_exposure"],
        "query_timeout": 2000  # 2 segundos max
    },
    "Trader": {
        "aggregation_level": "real_time",
        "detail_level": "granular",
        "index_hints": ["idx_timestamp_trade", "idx_currency_pair"],
        "query_timeout": 1000  # 1 segundo max
    }
}
```

## 🎯 Vantagens da Integração

### **Aproveitamento Máximo do Existente**
- ✅ **UnifiedCacheSystem**: Estender sem reescrever
- ✅ **SnapshotService**: Adicionar dimensões por perfil
- ✅ **QueryCache**: Usar embeddings existentes
- ✅ **KpiRepository**: Aproveitar SQLModel patterns

### **Fail Fast Nativo**
- ✅ **Sem Fallbacks**: Entre camadas de dados
- ✅ **Validação Obrigatória**: Dados corrompidos = falha
- ✅ **Profile Required**: Sem perfil = sem acesso
- ✅ **Timeout Agressivo**: Queries lentas = falha

### **Performance Otimizada**
- ✅ **3 Camadas**: Snapshot → Cache → Query direta
- ✅ **TTL Inteligente**: Baseado no perfil e criticidade
- ✅ **Índices Específicos**: Por padrão de uso do perfil
- ✅ **Prefetch**: Para dados críticos por perfil

## 🚀 Próximos Passos

### **Implementação Imediata**
1. **Criar branch**: `feature/fase1-arquitetura-hibrida`
2. **Estender snapshots**: Adicionar dimensões por perfil
3. **Implementar router**: SmartQueryRouter fail-fast
4. **Cache personalizado**: Estender UnifiedCacheSystem
5. **Integrar tudo**: No workflow LangGraph existente

### **Ordem de Implementação**
```yaml
semana_1:
  - kpi_snapshots_v2_model
  - personalized_cache_system
  - profile_cache_strategies

semana_2:
  - smart_query_router
  - optimized_query_service
  - fail_fast_validations

semana_3:
  - integracao_langgraph_workflow
  - testes_performance_camadas
  - documentacao_arquitetura
```

---

*Esta análise mostra que o codebase DataHero4 já tem 70% da arquitetura híbrida implementada. A integração fail-fast pode ser feita aproveitando componentes existentes e adicionando apenas as extensões necessárias para personalização por perfil.*
